/**
 * n8n Code节点 - 基金数据处理和HTML生成
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 将n8n输入数据处理并生成完整的HTML字符串
 */

// 获取输入数据
const inputData = $input.all();

/**
 * 合并所有输入数据到一个对象
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 将分散的输入数据合并为统一对象
 */
function mergeInputData(data) {
  const merged = {};
  
  if (Array.isArray(data)) {
    data.forEach(item => {
      if (item.json) {
        Object.assign(merged, item.json);
      } else {
        Object.assign(merged, item);
      }
    });
  }
  
  return merged;
}

/**
 * 格式化日期显示
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 格式化日期为中文显示
 */
function formatDate(dateStr) {
  if (!dateStr || dateStr === '-' || dateStr === null) {
    return '-';
  }
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      return '-';
    }
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
  } catch (error) {
    return '-';
  }
}

/**
 * 格式化收益率显示
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 格式化收益率为百分比显示
 */
function formatReturn(value) {
  if (!value || value === '-' || value === null || value === undefined) {
    return '-';
  }
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '-';
  }
  return num >= 0 ? `+${num.toFixed(4)}%` : `${num.toFixed(4)}%`;
}

/**
 * 获取收益率颜色样式
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 根据收益率正负返回对应颜色
 * @modified 2025-01-27T10:30:00.000Z jason 修改颜色规则：正数红色，负数绿色
 */
function getReturnColor(value) {
  if (!value || value === '-' || value === null || value === undefined) {
    return 'color: #666;';
  }
  const num = parseFloat(value);
  if (isNaN(num)) {
    return 'color: #666;';
  }
  // 修改颜色规则：正数显示红色，负数显示绿色
  return num >= 0 ? 'color: red;' : 'color: green;';
}

/**
 * 格式化年限显示
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 将天数转换为年限显示
 */
function formatYears(days) {
  if (!days || days === '-' || days === null || days === undefined) {
    return '-';
  }
  const years = Math.floor(parseInt(days) / 365);
  return years > 0 ? `${years}年` : '不足1年';
}

/**
 * 生成行业配置表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成行业配置数据表格
 */
function generateIndustryRows(data) {
  let rows = '';
  for (let i = 1; i <= 10; i++) {
    const industry = data[`top_industry_${i}`];
    const ratio = data[`industry_ratio_${i}`];
    
    if (industry && industry !== '-' && industry !== null) {
      rows += `
        <tr>
          <td>${industry}</td>
          <td>${ratio ? ratio + '%' : '-'}</td>
        </tr>
      `;
    }
  }
  return rows;
}

/**
 * 生成重仓股票表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成重仓股票数据表格
 */
function generateStockRows(data) {
  let rows = '';
  for (let i = 1; i <= 10; i++) {
    const code = data[`stock_code_${i}`];
    const name = data[`stock_name_${i}`];
    const marketValue = data[`stock_market_value_${i}`];
    const ratio = data[`stock_ratio_${i}`];
    const quantity = data[`stock_quantity_${i}`];
    
    if (code && name && code !== '-' && name !== '-') {
      rows += `
        <tr>
          <td>${code}</td>
          <td>${name}</td>
          <td>${marketValue || '-'}</td>
          <td>${ratio ? ratio + '%' : '-'}</td>
          <td>${quantity || '-'}</td>
        </tr>
      `;
    }
  }
  return rows;
}

/**
 * 生成重仓债券表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成重仓债券数据表格
 */
function generateBondRows(data) {
  let rows = '';
  for (let i = 1; i <= 5; i++) {
    const code = data[`bond_code_${i}`];
    const name = data[`bond_name_${i}`];
    const marketValue = data[`bond_market_value_${i}`];
    const ratio = data[`bond_ratio_${i}`];
    const quantity = data[`bond_quantity_${i}`];
    
    if (code && name && code !== '-' && name !== '-') {
      rows += `
        <tr>
          <td>${code}</td>
          <td>${name}</td>
          <td>${marketValue || '-'}</td>
          <td>${ratio ? ratio + '%' : '-'}</td>
          <td>${quantity || '-'}</td>
        </tr>
      `;
    }
  }
  return rows;
}

/**
 * 生成基金经理收益率表现表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成基金经理收益率表现数据
 * @modified 2025-01-27T10:30:00.000Z - 修复performanceData数组定义和数据映射
 */
function generateManagerPerformanceRows(data) {
  let rows = '';
  
  // 基金经理收益率数据映射
  const performanceData = [
    { label: '今年以来', field: 'ytd_total_return' },
    { label: '近1个月', field: 'month_1_total_return' },
    { label: '近3个月', field: 'month_3_total_return' },
    { label: '近6个月', field: 'month_6_total_return' },
    { label: '近1年', field: 'year_1_total_return' },
    { label: '近2年', field: 'year_2_total_return' },
    { label: '近3年', field: 'year_3_total_return' },
    { label: '几何年化收益率', field: 'geometric_annual_return' }
  ];
  
  // 调试输出：检查数据结构
  console.log('基金经理收益率数据:', data);
  
  performanceData.forEach(item => {
    const value = data[item.field];
    console.log(`${item.label}: ${value}`); // 调试输出
    
    if (value !== undefined && value !== null && value !== '-' && value !== '') {
      rows += `
        <tr>
          <td>${item.label}</td>
          <td style="${getReturnColor(value)}">${formatReturn(value)}</td>
        </tr>
      `;
    }
  });
  
  // 如果没有任何数据，显示提示信息
  if (rows === '') {
    rows = `
      <tr>
        <td colspan="2" style="text-align: center; color: #666;">暂无收益率数据</td>
      </tr>
    `;
  }
  
  return rows;
}

/**
 * 生成第二位基金经理收益率表现表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成第二位基金经理收益率表现数据
 * @param {Array} inputData - 输入数据数组
 * @param {string} managerName - 第二位基金经理姓名
 * @returns {string} 第二位基金经理收益率表格HTML字符串
 * @modified 2025-01-27T10:30:00.000Z jason 修复数据获取逻辑：从独立的基金经理数据对象中获取收益率数据
 */
function generateManager2ndPerformanceRows(inputData, managerName) {
  let rows = '';
  
  // 查找第二位基金经理的收益率数据
  const manager2ndData = inputData.find(item => {
    const data = item.json || item;
    return data.manager_name === managerName;
  });
  
  if (!manager2ndData) {
    return `
      <tr>
        <td colspan="2" style="text-align: center; color: #666;">暂无收益率数据</td>
      </tr>
    `;
  }
  
  const data = manager2ndData.json || manager2ndData;
  
  // 第二位基金经理收益率数据映射
  const performanceData = [
    { label: '今年以来', field: 'ytd_total_return' },
    { label: '近1个月', field: 'month_1_total_return' },
    { label: '近3个月', field: 'month_3_total_return' },
    { label: '近6个月', field: 'month_6_total_return' },
    { label: '近1年', field: 'year_1_total_return' },
    { label: '近2年', field: 'year_2_total_return' },
    { label: '近3年', field: 'year_3_total_return' },
    { label: '几何年化收益率', field: 'geometric_annual_return' }
  ];
  
  // 调试输出：检查第二位基金经理数据结构
  console.log('第二位基金经理收益率数据:', data);
  
  performanceData.forEach(item => {
    const value = data[item.field];
    console.log(`${item.label}: ${value}`); // 调试输出
    
    if (value !== undefined && value !== null && value !== '-' && value !== '') {
      rows += `
        <tr>
          <td>${item.label}</td>
          <td style="${getReturnColor(value)}">${formatReturn(value)}</td>
        </tr>
      `;
    }
  });
  
  // 如果没有任何数据，显示提示信息
  if (rows === '') {
    rows = `
      <tr>
        <td colspan="2" style="text-align: center; color: #666;">暂无收益率数据</td>
      </tr>
    `;
  }
  
  return rows;
}

/**
 * 生成基金经理管理的基金表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成基金经理管理基金列表
 * @modified 2025-01-27T10:30:00.000Z jason 修复基金列表数据获取逻辑，支持中文字段名
 */
function generateManagerFundRows(inputData) {
  let rows = '';
  
  // 查找基金列表数据 - 支持多种数据格式
  const fundEntries = [];
  
  inputData.forEach(item => {
    const data = item.json || item;
    
    // 处理中文字段名的基金资产净值数据
    if (data['基金代码'] && data['基金名称'] && data['基金资产净值']) {
      const scale = data['基金资产净值'].replace('亿元', '');
      fundEntries.push({
        name: data['基金名称'],
        scale: scale
      });
    }
    // 处理英文字段名的基金数据
    else if (data.fund_code && data.fund_name) {
      fundEntries.push({
        name: data.fund_name,
        scale: data.net_asset_value || data['基金资产净值']?.replace('亿元', '')
      });
    }
  });
  
  // 去重并按规模排序
  const uniqueFunds = [];
  const seenNames = new Set();
  
  fundEntries.forEach(fund => {
    if (!seenNames.has(fund.name)) {
      seenNames.add(fund.name);
      uniqueFunds.push(fund);
    }
  });
  
  // 按规模排序（数值型排序）
  uniqueFunds.sort((a, b) => {
    const scaleA = parseFloat(a.scale) || 0;
    const scaleB = parseFloat(b.scale) || 0;
    return scaleB - scaleA;
  });
  
  // 生成表格行，最多显示10个
  uniqueFunds.slice(0, 10).forEach(fund => {
    rows += `
      <tr>
        <td><a href="#" class="fund-link">${fund.name}</a></td>
        <td>${fund.scale ? fund.scale + '亿元' : '-'}</td>
      </tr>
    `;
  });
  
  // 如果没有数据，显示提示信息
  if (rows === '') {
    rows = `
      <tr>
        <td colspan="2" style="text-align: center; color: #666;">暂无管理基金数据</td>
      </tr>
    `;
  }
  
  return rows;
}

/**
 * 生成第二位基金经理管理的基金表格行
 * <AUTHOR>
 * @date 2025-01-27T10:30:00.000Z
 * @description 生成指定基金经理管理基金列表
 * @param {Array} inputData - 输入数据数组
 * @param {string} managerName - 基金经理姓名
 * @returns {string} 基金经理管理基金表格HTML字符串
 * @modified 2025-01-27T10:30:00.000Z jason 修改为通用函数，支持根据基金经理姓名匹配基金数据
 */
function generateManager2ndFundRows(inputData, managerName) {
  let rows = '';
  
  // 查找指定基金经理的基金列表数据
  const fundEntries = [];
  
  inputData.forEach(item => {
    const data = item.json || item;
    
    // 处理中文字段名的基金数据 - 根据基金经理姓名匹配
    if (data['基金经理'] === managerName && data['基金代码'] && data['基金名称'] && data['基金资产净值']) {
      const scale = data['基金资产净值'].replace('亿元', '');
      fundEntries.push({
        name: data['基金名称'],
        scale: scale
      });
    }
    // 处理英文字段名的基金数据
    else if (data.fund_manager === managerName && data.fund_code && data.fund_name) {
      fundEntries.push({
        name: data.fund_name,
        scale: data.net_asset_value || data['基金资产净值']?.replace('亿元', '')
      });
    }
  });
  
  // 去重并按规模排序
  const uniqueFunds = [];
  const seenNames = new Set();
  
  fundEntries.forEach(fund => {
    if (!seenNames.has(fund.name)) {
      seenNames.add(fund.name);
      uniqueFunds.push(fund);
    }
  });
  
  // 按规模排序（数值型排序）
  uniqueFunds.sort((a, b) => {
    const scaleA = parseFloat(a.scale) || 0;
    const scaleB = parseFloat(b.scale) || 0;
    return scaleB - scaleA;
  });
  
  // 生成表格行，最多显示10个
  uniqueFunds.slice(0, 10).forEach(fund => {
    rows += `
      <tr>
        <td><a href="#" class="fund-link">${fund.name}</a></td>
        <td>${fund.scale ? fund.scale + '亿元' : '-'}</td>
      </tr>
    `;
  });
  
  // 如果没有数据，显示提示信息
  if (rows === '') {
    rows = `
      <tr>
        <td colspan="2" style="text-align: center; color: #666;">暂无管理基金数据</td>
      </tr>
    `;
  }
  
  return rows;
}

// 合并数据
const mergedData = mergeInputData(inputData);

// 处理基金经理名称
let managerNames = mergedData.manager_name_1st || '-';
if (mergedData.manager_name_2nd) {
  managerNames += '，' + mergedData.manager_name_2nd;
}

// 生成完整的HTML
const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>基金数据分析</title>
  <style>
    /* 全局样式 */
    body {
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      margin: 0;
      padding: 0;
      color: #333;
      line-height: 1.5;
    }

    .container {
      margin: 0 auto;
      padding: 20px;
    }

    h3 {
      color: #1e293b;
      font-size: 18px;
      font-weight: 600;
      margin: 16px 0;
      padding-left: 8px;
      border-left: 4px solid #0ea5e9;
    }

    /* 基金信息部分 */
    .fund-header {
      margin-bottom: 16px;
    }

    .fund-type,
    .fund-date,
    .fund-index,
    .fund-company,
    .fund-manager,
    .fund-connection,
    .metric {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .fund-index {
      font-weight: normal;
    }

    .fund-connection {
      color: #555;
      margin-bottom: 16px;
    }

    .fund-metrics {
      margin-bottom: 20px;
    }

    .metrics-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .metric {
      flex: 1;
      margin-right: 20px;
    }

    .metric:last-child {
      margin-right: 0;
    }

    .value {
      font-weight: 600;
    }

    /* 表格样式 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 16px;
      font-size: 14px;
    }

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      background-color: #f8fafc;
      font-weight: 600;
      color: #374151;
    }

    /* 收益表现和行业配置 */
    .performance-industry {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .performance-section,
    .industry-section {
      flex: 1;
    }

    .performance-table,
    .industry-table {
      font-size: 14px;
    }

    /* 重仓股票和债券 */
    .holdings-section {
      margin-bottom: 20px;
    }

    .holdings-table {
      font-size: 14px;
    }

    /* 基金经理部分 */
    .manager-section {
      margin-top: 30px;
      border-top: 2px solid #e5e7eb;
      padding-top: 20px;
    }

    .section-title {
      font-size: 20px;
      margin-bottom: 20px;
    }

    .manager-profile {
      margin-bottom: 20px;
    }

    .manager-name-section {
      margin-bottom: 16px;
    }

    .manager-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .gender,
    .education {
      font-size: 14px;
      color: #666;
      margin-left: 10px;
    }

    .manager-content {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }

    .manager-stats {
      flex: 1;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      font-size: 13px;
    }

    .stat-label {
      color: #666;
    }

    .stat-value {
      font-weight: 600;
    }

    .manager-bio-section {
      flex: 2;
      margin-top: -8px; /* 上移对齐投资经理年限 */
      margin-top: -8px; /* 上移对齐投资经理年限 */
      margin-top: -8px; /* 上移对齐投资经理年限 */
      margin-top: -8px; /* 上移对齐投资经理年限 */
    }

    .bio-title {;
      margin-top: 0 /* 确保标题顶部对齐 */
      font-size: 14px;;
      margin-top: 0 /* 确保标题顶部对齐 */
      font-weight: 600;;
      margin-top: 0 /* 确保标题顶部对齐 */
      margin-bottom: 8px;
      margin-top: 0; /* 确保标题顶部对齐 */
    }

    .manager-bio {
      font-size: 13px;
      line-height: 1.6;
      color: #555;
      max-height: 120px;
      overflow-y: auto;
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      background-color: #f8fafc;
    }

    .manager-bio::-webkit-scrollbar {
      width: 6px;
    }

    .manager-bio::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    .manager-bio::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
    }

    .manager-bio::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }

    /* 收益率表现和管理基金 */
    .performance-funds {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .manager-performance,
    .top-funds {
      flex: 1;
    }

    .calculation-note {
      font-size: 11px;
      color: #666;
      margin-top: 8px;
      line-height: 1.4;
    }

    .fund-link {
      color: #0ea5e9;
      text-decoration: none;
    }

    .fund-link:hover {
      text-decoration: underline;
    }

    /* 基金公司信息 */
    .company-section {
      margin-top: 20px;
    }

    .company-info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
    }

    .company-info-block {
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      overflow: hidden;
    }

    .section-subtitle {
      background-color: #f8fafc;
      padding: 12px;
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      border-bottom: 1px solid #e5e7eb;
    }

    .company-content {
      padding: 12px;
    }

    .inner-table {
      margin: 0;
    }

    .compact-table td {
      padding: 6px 8px;
    }

    .info-label {
      color: #666;
      width: 80px;
    }

    .info-value {
      font-weight: 500;
    }

    .text-overflow {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .shareholders-table th {
      background-color: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      padding: 10px 15px;
      font-size: 14px;
      color: #1e293b;
      font-weight: 600;
      position: sticky;
      top: 0;
      z-index: 1;
      box-shadow: none;
      text-shadow: none;
    }

    .shareholders-table td {
      padding: 6px 8px;
    }

    .shareholder-name {
      width: 60%;
    }

    .shareholder-ratio {
      width: 40%;
    }

    .website-link {
      color: #0ea5e9;
      text-decoration: none;
    }

    .website-link:hover {
      text-decoration: underline;
    }

    /* 基金公司和基金经理的value显示为蓝色 */
    .fund-company .value,
    .fund-manager .value {
      color: #0ea5e9;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 上半部分：基金基本信息 -->
    <div class="fund-info-section">
      <div class="fund-header">
        <div class="fund-type">基金类型：${mergedData.investment_type || '-'}</div>
        <div class="fund-date">成立日期：${formatDate(mergedData.establishment_date)}</div>
        <div class="fund-index">指数名称：${mergedData.tracking_index_name || '-'}</div>
        <div class="fund-company">基金公司：<span class="value">${mergedData.management_company_name || '-'}</span></div>
        <div class="fund-manager">基金经理：<span class="value">${managerNames}</span></div>
        <div class="fund-connection">联接基金：${mergedData.related_fund_code || '-'}</div>
      </div>

      <div class="fund-metrics">
        <div class="metrics-row">
          <div class="metric">总资产规模：<span class="value">${mergedData.net_asset_value ? mergedData.net_asset_value + '亿元' : '-'}</span></div>
          <div class="metric">基金净值：<span class="value">${mergedData.unit_net_value || '-'}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">基金场内规模：<span class="value">-</span></div>
          <div class="metric">收盘价格：<span class="value">${mergedData.closing_price || '-'}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">管理费用率：<span class="value">${mergedData.management_fee_rate ? mergedData.management_fee_rate + '%' : '-'}</span></div>
          <div class="metric">收盘价涨跌幅：<span class="value" style="${getReturnColor(mergedData.price_change_pct)}">${mergedData.price_change_pct || '-'}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">托管费用率：<span class="value">${mergedData.custody_fee_rate ? mergedData.custody_fee_rate + '%' : '-'}</span></div>
          <div class="metric">成交金额：<span class="value">${mergedData.trading_volume ? (mergedData.trading_volume / 100000000).toFixed(2) + '亿元' : '-'}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">持仓换手率：<span class="value">${mergedData.turnover_rate ? mergedData.turnover_rate + '%' : '-'}</span></div>
          <div class="metric">折价率：<span class="value">${mergedData.premium_discount_rate ? mergedData.premium_discount_rate + '%' : '-'}</span></div>
        </div>
      </div>

      <!-- 收益表现和行业配置 -->
      <div class="performance-industry">
        <div class="performance-section">
          <h3>收益率表现</h3>
          <table class="performance-table">
            <tr><th>基金业绩</th><th></th></tr>
            <tr><td>今年以来：</td><td style="${getReturnColor(mergedData.ytd_total_return)}">${formatReturn(mergedData.ytd_total_return)}</td></tr>
            <tr><td>近一个月：</td><td style="${getReturnColor(mergedData.month_1_total_return)}">${formatReturn(mergedData.month_1_total_return)}</td></tr>
            <tr><td>近三个月：</td><td style="${getReturnColor(mergedData.month_3_total_return)}">${formatReturn(mergedData.month_3_total_return)}</td></tr>
            <tr><td>近六个月：</td><td style="${getReturnColor(mergedData.month_6_total_return)}">${formatReturn(mergedData.month_6_total_return)}</td></tr>
            <tr><td>近1年：</td><td style="${getReturnColor(mergedData.year_1_total_return)}">${formatReturn(mergedData.year_1_total_return)}</td></tr>
            <tr><td>近2年：</td><td style="${getReturnColor(mergedData.year_2_total_return)}">${formatReturn(mergedData.year_2_total_return)}</td></tr>
            <tr><td>近3年：</td><td style="${getReturnColor(mergedData.year_3_total_return)}">${formatReturn(mergedData.year_3_total_return)}</td></tr>
            <tr><td>近5年：</td><td style="${getReturnColor(mergedData.year_5_total_return)}">${formatReturn(mergedData.year_5_total_return)}</td></tr>
            <tr><td>成立以来：</td><td style="${getReturnColor(mergedData.since_inception_return)}">${formatReturn(mergedData.since_inception_return)}</td></tr>
          </table>
        </div>

        <div class="industry-section">
          <h3>行业配置</h3>
          <table class="industry-table">
            <tr><th>行业名称</th><th>占净值比</th></tr>
            ${generateIndustryRows(mergedData)}
          </table>
        </div>
      </div>

      <!-- 重仓股票和重仓债券 -->
      <div class="holdings-section">
        <h3>重仓股票（TOP10）</h3>
        <table class="holdings-table">
          <tr><th>证券代码</th><th>证券简称</th><th>持仓市值(元)</th><th>占基金净值比</th><th>持仓数量</th></tr>
          ${generateStockRows(mergedData)}
        </table>

        <h3>重仓债券（TOP5）</h3>
        <table class="holdings-table">
          <tr><th>债券代码</th><th>债券简称</th><th>持仓市值(元)</th><th>占基金净值比</th><th>持仓数量</th></tr>
          ${generateBondRows(mergedData)}
        </table>
      </div>
    </div>

    <!-- 下半部分：基金经理信息 -->
    <div class="manager-section">
      <h3 class="section-title">基金经理-现任</h3>
      
      <!-- 第一位基金经理完整信息块 -->
      <div class="manager-profile">
        <div class="manager-name-section">
          <div class="manager-name">
            <span>${mergedData.manager_name_1st || '-'}</span>
            <span class="gender">${mergedData.gender_1st || '-'}</span>
            <span class="education">${mergedData.education_1st || '-'}</span>
          </div>
          <div class="manager-content">
            <div class="manager-stats">
              <div class="stats-grid">
                <div class="stat-label">投资经理年限</div>
                <div class="stat-value">${mergedData.tenure_years_1st ? mergedData.tenure_years_1st + '年' : '-'}</div>
                <div class="stat-label">历任管理基金数</div>
                <div class="stat-value">${mergedData.historical_fund_count_1st ? mergedData.historical_fund_count_1st + '只' : '-'}</div>
                <div class="stat-label">在任基金总规模</div>
                <div class="stat-value">${mergedData.total_fund_scale_1st ? mergedData.total_fund_scale_1st + '万元' : '-'}</div>
                <div class="stat-label">现任公司年限</div>
                <div class="stat-value">${formatYears(mergedData.tenure_days_1st)}</div>
                <div class="stat-label">在任管理基金数</div>
                <div class="stat-value">${mergedData.current_fund_count_1st ? mergedData.current_fund_count_1st + '只' : '-'}</div>
                <div class="stat-label">获奖数</div>
                <div class="stat-value">${mergedData.award_count_1st ? mergedData.award_count_1st + '个' : '-'}</div>
              </div>
            </div>
            <div class="manager-bio-section">
              <h4 class="bio-title">基金经理简介:</h4>
              <div class="manager-bio">
                <p>${mergedData.resume_1st || '-'}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 第一位基金经理收益率表现和管理基金 -->
        <div class="performance-funds" style="margin-top: 20px;">
          <div class="manager-performance">
            <h3>${mergedData.manager_name_1st || '基金经理'}收益率表现</h3>
            <table class="performance-table">
              <tr><th>任职间收益率</th><th></th></tr>
              ${generateManager2ndPerformanceRows(inputData, mergedData.manager_name_1st)}
            </table>
            <div class="calculation-note">
              收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。
            </div>
          </div>

          <div class="top-funds">
            <h3>${mergedData.manager_name_1st || '基金经理'}前十大管理基金</h3>
            <table class="funds-table">
              <tr><th>基金名称</th><th>总资产规模</th></tr>
              ${generateManager2ndFundRows(inputData, mergedData.manager_name_1st)}
            </table>
          </div>
        </div>
      </div>

      ${mergedData.manager_name_2nd ? `
      <!-- 第二位基金经理完整信息块 -->
      <div class="manager-profile" style="margin-top: 40px; border-top: 2px solid #e5e7eb; padding-top: 30px;">
        <div class="manager-name-section">
          <div class="manager-name">
            <span>${mergedData.manager_name_2nd}</span>
            <span class="gender">${mergedData.gender_2nd || '-'}</span>
            <span class="education">${mergedData.education_2nd || '-'}</span>
          </div>
          <div class="manager-content">
            <div class="manager-stats">
              <div class="stats-grid">
                <div class="stat-label">投资经理年限</div>
                <div class="stat-value">${mergedData.tenure_years_2nd ? mergedData.tenure_years_2nd + '年' : '-'}</div>
                <div class="stat-label">历任管理基金数</div>
                <div class="stat-value">${mergedData.historical_fund_count_2nd ? mergedData.historical_fund_count_2nd + '只' : '-'}</div>
                <div class="stat-label">在任基金总规模</div>
                <div class="stat-value">${mergedData.total_fund_scale_2nd ? mergedData.total_fund_scale_2nd + '万元' : '-'}</div>
                <div class="stat-label">现任公司年限</div>
                <div class="stat-value">${formatYears(mergedData.tenure_days_2nd)}</div>
                <div class="stat-label">在任管理基金数</div>
                <div class="stat-value">${mergedData.current_fund_count_2nd ? mergedData.current_fund_count_2nd + '只' : '-'}</div>
                <div class="stat-label">获奖数</div>
                <div class="stat-value">${mergedData.award_count_2nd ? mergedData.award_count_2nd + '个' : '-'}</div>
              </div>
            </div>
            <div class="manager-bio-section">
              <h4 class="bio-title">基金经理简介:</h4>
              <div class="manager-bio">
                <p>${mergedData.resume_2nd || '-'}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二位基金经理收益率表现和管理基金 -->
        <div class="performance-funds" style="margin-top: 20px;">
          <div class="manager-performance">
            <h3>${mergedData.manager_name_2nd}收益率表现</h3>
            <table class="performance-table">
              <tr><th>任职间收益率</th><th></th></tr>
              ${generateManager2ndPerformanceRows(inputData, mergedData.manager_name_2nd)}
            </table>
            <div class="calculation-note">
              收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。
            </div>
          </div>

          <div class="top-funds">
            <h3>${mergedData.manager_name_2nd}前十大管理基金</h3>
            <table class="funds-table">
              <tr><th>基金名称</th><th>总资产规模</th></tr>
              ${generateManager2ndFundRows(inputData, mergedData.manager_code_2nd)}
            </table>
          </div>
        </div>
      </div>
      ` : ''}

      <!-- 基金公司信息 -->
      <div class="company-section">
        <h3>${mergedData.management_company_name || '基金公司'}</h3>
        <div class="company-info-grid">
          <div class="company-info-block">
            <h4 class="section-subtitle">基本资料</h4>
            <div class="company-content company-basic-info">
              <table class="inner-table compact-table">
                <tr><td class="info-label">成立日期</td><td class="info-value">${formatDate(mergedData.establishment_date)}</td></tr>
                <tr><td class="info-label">注册资本(万)</td><td class="info-value">${mergedData.registered_capital || '-'}</td></tr>
                <tr><td class="info-label">总经理</td><td class="info-value">${mergedData.general_manager || '-'}</td></tr>
                <tr><td class="info-label">管理资产规模</td><td class="info-value">${mergedData.total_net_asset_value ? mergedData.total_net_asset_value + '亿元' : '-'}</td></tr>
                <tr><td class="info-label">旗下基金数量</td><td class="info-value">${mergedData.fund_count || '-'}</td></tr>
                <tr><td class="info-label">基金经理人数</td><td class="info-value">${mergedData.manager_count || '-'}</td></tr>
              </table>
            </div>
          </div>

          <div class="company-info-block">
            <h4 class="section-subtitle">股东列表</h4>
            <div class="company-content shareholders-content">
              <table class="inner-table shareholders-table">
                <tr><th class="shareholder-name">股东名称</th><th class="shareholder-ratio">持股比例</th></tr>
                <tr><td class="shareholder-name">开发中</td><td class="shareholder-ratio">-</td></tr>
              </table>
            </div>
          </div>

          <div class="company-info-block">
            <h4 class="section-subtitle">联系方式</h4>
            <div class="company-content company-contact-info">
              <table class="inner-table compact-table">
                <tr><td class="info-label">电话</td><td class="info-value">${mergedData.company_phone || mergedData.phone || '-'}</td></tr>
                <tr><td class="info-label">传真</td><td class="info-value">${mergedData.company_fax || mergedData.fax || '-'}</td></tr>
                <tr><td class="info-label">网址</td><td class="info-value">${mergedData.company_website || mergedData.website ? `<a href="${(mergedData.company_website || mergedData.website).startsWith('http') ? (mergedData.company_website || mergedData.website) : 'http://' + (mergedData.company_website || mergedData.website)}" target="_blank" class="website-link">${mergedData.company_website || mergedData.website}</a>` : '-'}</td></tr>
                <tr><td class="info-label">办公地址</td><td class="info-value">${mergedData.company_office_address || mergedData.office_address || '-'}</td></tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
`;

// 返回生成的HTML内容
return [{ 
  json: { 
   
 html: htmlContent,
    processedData: mergedData,
   
 timestamp: new Date().toISOString()
  } 
}];
























