{
  "name": "Data Agent 智能代理工作流",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "agent-query",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-input",
      "name": "用户查询输入",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "data-agent-query"
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "/**\n * 意图识别逻辑\n * <AUTHOR> * @created 2025-08-01\n * @description 解析用户查询意图\n */\n\n// 获取用户输入\nconst userInput = $input.all()[0].json;\nconst userQuery = userInput.body?.query || userInput.query || userInput.message || '';\n\n// 意图识别函数\nconst recognizeIntent = (query) => {\n  const q = query.toLowerCase().trim();\n  \n  if (q.includes('基金') || q.includes('fund') || /\\d{6}/.test(q)) {\n    return 'fund_query';\n  } else if (q.includes('经理') || q.includes('manager')) {\n    return 'manager_query';\n  } else if (q.includes('帮助') || q.includes('help') || q.includes('使用说明')) {\n    return 'help';\n  } else if (q.includes('分析') || q.includes('统计')) {\n    return 'data_analysis';\n  } else {\n    return 'unknown';\n  }\n};\n\nconst intent = recognizeIntent(userQuery);\n\nreturn [{\n  json: {\n    originalQuery: userQuery,\n    intent: intent,\n    confidence: intent === 'unknown' ? 0.1 : 0.8,\n    timestamp: new Date().toISOString(),\n    userId: userInput.userId || 'anonymous'\n  }\n}];"
      },
      "id": "intent-recognition",
      "name": "意图识别",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "/**\n * 参数提取逻辑\n * <AUTHOR> * @created 2025-08-01\n * @description 从用户查询中提取关键参数\n */\n\nconst data = $input.all()[0].json;\nconst query = data.originalQuery;\nconst intent = data.intent;\n\n// 参数提取函数\nconst extractParameters = (query, intent) => {\n  const params = {};\n  \n  // 提取基金代码 (6位数字)\n  const fundCodeMatch = query.match(/\\b\\d{6}\\b/);\n  if (fundCodeMatch) {\n    params.fundCode = fundCodeMatch[0];\n  }\n  \n  // 提取基金名称\n  const fundNameMatch = query.match(/([\\u4e00-\\u9fa5A-Za-z]+基金)/);\n  if (fundNameMatch) {\n    params.fundName = fundNameMatch[1];\n  }\n  \n  // 提取经理姓名\n  const managerNameMatch = query.match(/([\\u4e00-\\u9fa5]{2,4})/);\n  if (managerNameMatch && intent === 'manager_query') {\n    params.managerName = managerNameMatch[1];\n  }\n  \n  return params;\n};\n\nconst parameters = extractParameters(query, intent);\n\nreturn [{\n  json: {\n    ...data,\n    parameters: parameters,\n    hasValidParams: Object.keys(parameters).length > 0\n  }\n}];"
      },
      "id": "parameter-extraction",
      "name": "参数提取",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [680, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "fund-query-condition",
              "leftValue": "={{ $json.intent }}",
              "rightValue": "fund_query",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "intent-router",
      "name": "意图路由",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [900, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "/**\n * 基金查询处理逻辑\n * <AUTHOR> * @created 2025-08-01\n * @description 处理基金相关查询\n */\n\nconst data = $input.all()[0].json;\nconst params = data.parameters;\n\n// 模拟基金数据查询\nconst queryFundData = (fundCode, fundName) => {\n  // 这里可以替换为真实的API调用\n  const mockData = {\n    '000001': {\n      fundCode: '000001',\n      fundName: '华夏成长混合',\n      manager: '张三',\n      scale: '15.6亿',\n      type: '混合型基金',\n      netValue: '1.2345',\n      dayChange: '+0.56%'\n    },\n    '000002': {\n      fundCode: '000002',\n      fundName: '华夏优势增长混合',\n      manager: '李四',\n      scale: '8.9亿',\n      type: '混合型基金',\n      netValue: '0.9876',\n      dayChange: '-0.23%'\n    }\n  };\n  \n  if (fundCode && mockData[fundCode]) {\n    return mockData[fundCode];\n  }\n  \n  // 如果没有找到，返回默认信息\n  return {\n    fundCode: fundCode || 'N/A',\n    fundName: fundName || '未找到基金信息',\n    manager: '未知',\n    scale: '未知',\n    type: '未知',\n    netValue: '未知',\n    dayChange: '未知'\n  };\n};\n\nconst result = queryFundData(params.fundCode, params.fundName);\n\nreturn [{\n  json: {\n    ...data,\n    queryResult: result,\n    queryType: 'fund_data',\n    success: true\n  }\n}];"
      },
      "id": "fund-query-handler",
      "name": "基金查询处理",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "/**\n * 帮助信息处理逻辑\n * <AUTHOR> * @created 2025-08-01\n * @description 提供使用帮助信息\n */\n\nconst data = $input.all()[0].json;\n\nconst helpInfo = {\n  message: '欢迎使用Data Agent智能查询助手！',\n  features: [\n    '基金信息查询：输入基金代码或基金名称',\n    '基金经理查询：询问基金经理相关信息',\n    '数据分析：提供基础数据分析功能'\n  ],\n  examples: [\n    '查询基金 000001',\n    '华夏成长基金信息',\n    '基金经理张三',\n    '帮助'\n  ],\n  contact: '如需更多帮助，请联系技术支持'\n};\n\nreturn [{\n  json: {\n    ...data,\n    queryResult: helpInfo,\n    queryType: 'help_info',\n    success: true\n  }\n}];"
      },
      "id": "help-handler",
      "name": "帮助信息处理",
      "type": "n8n-nodes-base.code",\n      "typeVersion": 2,\n      "position": [1120, 400]\n    },\n    {\n      "parameters": {\n        "mode": "runOnceForAllItems",\n        "jsCode": "/**\\n * 响应生成逻辑\\n * <AUTHOR> * @created 2025-08-01\\n * @description 生成用户友好的回复\\n */\\n\\nconst data = $input.all()[0].json;\\nconst result = data.queryResult;\\nconst queryType = data.queryType;\\nconst intent = data.intent;\\n\\n// 响应生成函数\\nconst generateResponse = (intent, result, queryType) => {\\n  let message = '';\\n  let responseData = {};\\n  \\n  switch (queryType) {\\n    case 'fund_data':\\n      message = `📊 基金信息查询结果：\\n\\n` +\\n               `🏷️ 基金代码：${result.fundCode}\\n` +\\n               `📈 基金名称：${result.fundName}\\n` +\\n               `👤 基金经理：${result.manager}\\n` +\\n               `💰 基金规模：${result.scale}\\n` +\\n               `📋 基金类型：${result.type}\\n` +\\n               `💵 净值：${result.netValue}\\n` +\\n               `📊 日涨跌：${result.dayChange}`;\\n      responseData = result;\\n      break;\\n      \\n    case 'help_info':\\n      message = `${result.message}\\n\\n` +\\n               `🔧 功能介绍：\\n${result.features.map(f => `• ${f}`).join('\\n')}\\n\\n` +\\n               `💡 使用示例：\\n${result.examples.map(e => `• ${e}`).join('\\n')}\\n\\n` +\\n               `📞 ${result.contact}`;\\n      responseData = result;\\n      break;\\n      \\n    default:\\n      message = '抱歉，我无法理解您的请求。请输入"帮助"获取使用说明。';\\n      responseData = { error: 'Unknown query type' };\\n  }\\n  \\n  return {\\n    success: true,\\n    message: message,\\n    data: responseData,\\n    timestamp: new Date().toISOString(),\\n    responseType: queryType\\n  };\\n};\\n\\nconst response = generateResponse(intent, result, queryType);\\n\\nreturn [{\\n  json: response\\n}];"
      },
      "id": "response-generator",
      "name": "响应生成",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1340, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ $json }}"
      },
      "id": "webhook-response",
      "name": "返回响应",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1560, 300]
    }
  ],
  "connections": {
    "用户查询输入": {
      "main": [
        [
          {
            "node": "意图识别",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "意图识别": {
      "main": [
        [
          {
            "node": "参数提取",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "参数提取": {
      "main": [
        [
          {
            "node": "意图路由",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "意图路由": {
      "main": [
        [
          {
            "node": "基金查询处理",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "帮助信息处理",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "基金查询处理": {
      "main": [
        [
          {
            "node": "响应生成",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "帮助信息处理": {
      "main": [
        [
          {
            "node": "响应生成",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "响应生成": {
      "main": [
        [
          {
            "node": "返回响应",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2025-08-01T14:51:59.000Z",
      "updatedAt": "2025-08-01T14:51:59.000Z",
      "id": "data-agent",
      "name": "data-agent"
    }
  ],
  "triggerCount": 1,
  "updatedAt": "2025-08-01T14:51:59.000Z",
  "versionId": "1"
}
