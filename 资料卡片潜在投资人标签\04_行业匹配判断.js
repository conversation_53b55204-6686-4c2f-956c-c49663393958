/**
 * 行业匹配判断
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 判断基金行业是否与股票行业匹配
 */

try {
  // 获取上游数据
  const stockIndustry = $('查询股票行业').first().json.stockIndustry;
  const fundIndustries = $('查询基金行业').first().json.fundIndustries || [];
  
  if (!stockIndustry) {
    throw new Error('缺少股票行业信息');
  }
  
  if (fundIndustries.length === 0) {
    return {
      errorcode: 0,
      errmsg: "",
      message: "没有需要匹配的基金行业数据",
      matchedFunds: [],
      matchedCount: 0
    };
  }

  const matchedFunds = [];
  
  // 遍历每只基金进行行业匹配
  for (const fundData of fundIndustries) {
    const { investorCode, industries, originalFundData } = fundData;
    
    // 检查基金的任意一个行业是否与股票行业匹配
    const hasMatch = industries.some(item => 
      item.industry && item.industry.includes(stockIndustry)
    );
    
    if (hasMatch) {
      // 找到匹配的行业
      const matchedIndustries = industries.filter(item => 
        item.industry && item.industry.includes(stockIndustry)
      );
      
      matchedFunds.push({
        investorCode: investorCode,
        companyFilterId: fundData.companyFilterId,
        organizationId: fundData.organizationId,
        stockIndustry: stockIndustry,
        matchedIndustries: matchedIndustries,
        allIndustries: industries,
        originalFundData: originalFundData
      });
    }
  }

  return {
    errorcode: 0,
    errmsg: "",
    message: `找到${matchedFunds.length}只符合条件的潜在投资人基金`,
    stockIndustry: stockIndustry,
    matchedFunds: matchedFunds,
    matchedCount: matchedFunds.length,
    totalChecked: fundIndustries.length
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `行业匹配判断异常: ${error.message}`,
    matchedFunds: [],
    matchedCount: 0
  };
}