[{"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：偏股混合型基金</div>\n        <div class=\"fund-date\">成立日期：\n          2007年09年10日</div>\n        <div class=\"fund-index\">指数名称：沪深300指数,上证国债指数</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">华夏基金管理有限公司</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">黄皓，郑煜</span></div>\n        <div class=\"fund-connection\">联接基金：015073.OF</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">15.29亿元</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">2.1850</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">1.2000%</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">0.2000%</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                +15.7923%\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                +12.0513%\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                +19.3989%\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                +14.4578%\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                +32.4242%\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: green\">\n                -15.3101%\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: green\">\n                -33.8680%\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: green\">\n                -29.1505%\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                +118.5000%\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n            <tr>\n              <td>制造业</td>\n              <td>70.3123%</td>\n            </tr>\n            \n            <tr>\n              <td>交通运输、仓储和邮政业</td>\n              <td>11.4322%</td>\n            </tr>\n            \n            <tr>\n              <td>批发和零售业</td>\n              <td>3.4893%</td>\n            </tr>\n            \n            <tr>\n              <td>电力、热力、燃气及水生产和供应业</td>\n              <td>0.8083%</td>\n            </tr>\n            \n            <tr>\n              <td>科学研究和技术服务业</td>\n              <td>0.0009%</td>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                +15.7923%\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                +12.0513%\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                +19.3989%\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                +14.4578%\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                +32.4242%\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: green\">\n                -15.3101%\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: green\">\n                -33.8680%\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: green\">\n                -29.1505%\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                +118.5000%\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n            <tr>\n              <td>制造业</td>\n              <td>70.3123%</td>\n            </tr>\n            \n            <tr>\n              <td>交通运输、仓储和邮政业</td>\n              <td>11.4322%</td>\n            </tr>\n            \n            <tr>\n              <td>批发和零售业</td>\n              <td>3.4893%</td>\n            </tr>\n            \n            <tr>\n              <td>电力、热力、燃气及水生产和供应业</td>\n              <td>0.8083%</td>\n            </tr>\n            \n            <tr>\n              <td>科学研究和技术服务业</td>\n              <td>0.0009%</td>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">黄皓，郑煜</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          1998年04年09日</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">23800.0000万人民币</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">李一梅</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>黄皓</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                +9.4180%\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                +12.0141%\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                +11.2587%\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                +8.1627%\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                +20.1083%\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: green\">\n                -9.3708%\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: green\">\n                -8.5852%\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>郑煜</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：-</div>\n        <div class=\"fund-date\">成立日期：\n          -</div>\n        <div class=\"fund-index\">指数名称：-</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">-</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">-</span></div>\n        <div class=\"fund-connection\">联接基金：-</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: red\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">-</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">-</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">-</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: red\">\n                -\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          \n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>-</span>\n            <span class=\"gender\">-</span>\n          \n            <span class=\"education\">-</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">-</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">-</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>-</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"negative\">\n                -\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            \n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>基金公司</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                \n                <tr>\n                  <td class=\"shareholder-name text-overflow\">开发中</td>\n                  <td class=\"shareholder-ratio text-overflow\">-</td>\n                </tr>\n                \n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"#\" target=\"_blank\" class=\"website-link\">-</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">-</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}]