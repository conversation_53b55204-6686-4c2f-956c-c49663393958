/**
 * 查询基金投资行业
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 批量查询基金的前10名投资行业
 */

try {
  // 获取上游数据
  const accessToken = $('缓存acc token').first().json.access_token;
  const benchmarkFunds = $('筛选持有对标基金').first().json.benchmarkFunds || [];
  
  if (!accessToken) {
    throw new Error('缺少access_token');
  }
  
  if (benchmarkFunds.length === 0) {
    return {
      errorcode: 0,
      errmsg: "",
      message: "没有需要查询的基金",
      fundIndustries: [],
      processedCount: 0
    };
  }

  const fundIndustries = [];
  
  // 批量查询基金行业
  for (const fund of benchmarkFunds) {
    const investorCode = fund.investorCode;
    const fundIndustryList = [];
    
    try {
      // 查询前10名行业
      for (let rank = 1; rank <= 10; rank++) {
        const options = {
          url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'access_token': accessToken
          },
          body: {
            codes: investorCode,
            indipara: [{
              indicator: "ths_top_industry_name_fund",
              indiparams: ["8", rank.toString()]
            }]
          },
          json: true,
          timeout: 15000
        };

        const response = await this.helpers.httpRequest(options);
        
        if (response.errorcode === 0 && 
            response.tables && 
            response.tables.length > 0 && 
            response.tables[0].table &&
            response.tables[0].table.ths_top_industry_name_fund &&
            response.tables[0].table.ths_top_industry_name_fund.length > 0) {
          
          const industry = response.tables[0].table.ths_top_industry_name_fund[0];
          if (industry && industry !== "--" && industry.trim() !== "") {
            fundIndustryList.push({
              rank: rank,
              industry: industry
            });
          }
        }
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      fundIndustries.push({
        investorCode: investorCode,
        companyFilterId: fund.companyFilterId,
        organizationId: fund.organizationId || '',
        industries: fundIndustryList,
        originalFundData: fund
      });
      
    } catch (error) {
      console.log(`查询基金${investorCode}行业失败:`, error.message);
      // 继续处理下一个基金
    }
  }

  return {
    errorcode: 0,
    errmsg: "",
    message: `成功查询${fundIndustries.length}只基金的行业信息`,
    fundIndustries: fundIndustries,
    processedCount: fundIndustries.length,
    totalFunds: benchmarkFunds.length
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `查询基金行业异常: ${error.message}`,
    fundIndustries: [],
    processedCount: 0
  };
}