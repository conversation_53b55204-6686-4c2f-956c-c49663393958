/**
 * 投资人标签处理器 - 持有本司和对标公司标签自动打标
 * <AUTHOR>
 * @date 2025-7-10T15:22:31.000Z
 * 功能说明：
 * - 从基金数据中提取投资人代码（股东代码）
 * - 为持有本司股票的基金自动打上"持有本司"标签
 * - 为持有对标公司股票的基金自动打上"持有对标"标签
 * - 将标签数据写入InvestorTag表，包含companyFilterId关联
 * - 支持标签的创建和更新（防重复）
 */

try {
  // 获取输入数据
  const inputData = $input.all();
  if (!inputData || inputData.length === 0) {
    return {
      errorcode: -1,
      errmsg: "未接收到基金数据",
      tagResults: [],
      processedCount: 0,
      timestamp: new Date().toISOString()
    };
  }

  // 查找基金数据输入
  let fundDataInput = null;
  let companyFilterData = null;
  let organizationId = null;
  let stockCode = null;
  
  // 遍历所有输入，识别不同来源的数据
  for (const input of inputData) {
    // 识别基金数据
    if (input.json && (input.json.stockFundData || input.json.ben_stockCode)) {
      fundDataInput = input.json;
    } 
    // 识别公司筛选配置数据
    else if (input.json) {
      if (Array.isArray(input.json) && input.json.length > 0 && input.json[0].companyFilterId) {
        companyFilterData = input.json[0];
      } else if (input.json.companyFilterId) {
        companyFilterData = input.json;
      }
      
      // 尝试从输入中获取organizationId和stockCode
      if (input.json.organizationId) {
        organizationId = input.json.organizationId;
      }
      if (input.json.stockCode) {
        stockCode = input.json.stockCode;
      }
    }
  }
  
  if (!fundDataInput) {
    return {
      errorcode: -1,
      errmsg: "未找到有效的基金数据",
      tagResults: [],
      processedCount: 0,
      timestamp: new Date().toISOString()
    };
  }

  // 如果没有从输入中获取到organizationId和stockCode，尝试从工作流上下文获取
  if (!organizationId || !stockCode) {
    try {
      // 尝试从成功数据格式化节点获取
      const formatData = $('成功数据格式化').item.json;
      if (formatData) {
        organizationId = organizationId || formatData.organizationId;
        stockCode = stockCode || formatData.stockCode;
      }
    } catch (error) {
      console.log('获取成功数据格式化节点数据失败:', error.message);
      // 尝试从其他可能的节点获取
      try {
        const workflowData = $workflow.variables;
        if (workflowData) {
          organizationId = organizationId || workflowData.organizationId;
          stockCode = stockCode || workflowData.stockCode;
        }
      } catch (error2) {
        console.log('获取工作流变量失败:', error2.message);
      }
    }
    
    // 如果仍然没有，使用默认值
    organizationId = organizationId || $('查询公司筛选配置').first().json.organizationId
    stockCode = stockCode || $('查询公司筛选配置').first().json.companyCode
  }
  
  if (!organizationId || !stockCode) {
    return {
      errorcode: -1,
      errmsg: "缺少必要参数：organizationId或stockCode",
      tagResults: [],
      processedCount: 0,
      timestamp: new Date().toISOString()
    };
  }

  // 获取CompanyFilter查询结果
  let companyFilterId = null;
  if (companyFilterData && companyFilterData.companyFilterId) {
    companyFilterId = companyFilterData.companyFilterId;
    console.log('从输入数据中获取到companyFilterId:', companyFilterId);
  } else {
    try {
      // 尝试从检查筛选配置结果节点获取
      const filterCheckResult = $('检查筛选配置结果').item.json;
      if (filterCheckResult && filterCheckResult.id) {
        companyFilterId = filterCheckResult.id;
        console.log('从检查筛选配置结果节点获取到companyFilterId:', companyFilterId);
      } else if (filterCheckResult && filterCheckResult.companyFilterId) {
        companyFilterId = filterCheckResult.companyFilterId;
        console.log('从检查筛选配置结果节点获取到companyFilterId:', companyFilterId);
      }
    } catch (error) {
      console.log('获取检查筛选配置结果节点数据失败:', error.message);
      
      // 如果仍未获取到，尝试从其他来源获取
      try {
        const nodeData = $input.item.json;
        if (nodeData && nodeData.companyFilterId) {
          companyFilterId = nodeData.companyFilterId;
          console.log('从输入项获取到companyFilterId:', companyFilterId);
        }
      } catch (error2) {
        console.log('获取CompanyFilter数据失败:', error2.message);
        // 继续执行，但companyFilterId为null
      }
    }
  }

  // 准备标签数据
  const tagResults = [];
  const currentTime = new Date().toISOString();
  
  // 处理持有本司的基金数据
  if (fundDataInput.stockFundData && Array.isArray(fundDataInput.stockFundData)) {
    for (const fund of fundDataInput.stockFundData) {
      const investorCode = fund.shareholderCode;
      
      // 跳过无效的股东代码
      if (!investorCode || investorCode === "--" || investorCode.trim() === "") {
        continue;
      }
      
      // 构建标签数据
      const tagData = {
        organizationId: organizationId,
        companyFilterId: companyFilterId,
        investorCode: investorCode,
        tagName: `持有本司(${stockCode})`,
        tagCategory: "system",
        modifiedAt: currentTime,
        fundInfo: {
          stockCode: fund.stockCode,
          stockName: fund.stockName,
          shareholderName: fund.shareholderName,
          holdingAmount: fund.holdingAmount,
          holdingRatio: fund.holdingRatio
        }
      };
      
      tagResults.push(tagData);
    }
  }
  
  // 处理持有对标公司的基金数据
  if (fundDataInput.ben_stockCode && Array.isArray(fundDataInput.ben_stockCode)) {
    for (const benchmarkGroup of fundDataInput.ben_stockCode) {
      if (Array.isArray(benchmarkGroup)) {
        for (const fund of benchmarkGroup) {
          const investorCode = fund.shareholderCode;
          
          // 跳过无效的股东代码
          if (!investorCode || investorCode === "--" || investorCode.trim() === "") {
            continue;
          }
          
          // 构建标签数据
          const tagData = {
            organizationId: organizationId,
            companyFilterId: companyFilterId,
            investorCode: investorCode,
            tagName: `持有对标(${fund.stockCode})`,
            tagCategory: "system",
            modifiedAt: currentTime,
            fundInfo: {
              stockCode: fund.stockCode,
              stockName: fund.stockName,
              shareholderName: fund.shareholderName,
              holdingAmount: fund.holdingAmount,
              holdingRatio: fund.holdingRatio
            }
          };
          
          tagResults.push(tagData);
        }
      }
    }
  }

  // 构建SQL语句
  const sqlStatements = tagResults.map(tag => {
    return {
      sql: `
INSERT INTO investor_tag (
  "organizationId", "companyFilterId", "investorCode", "tagName", "tagCategory", "modifiedAt", "tagMetadata"
) VALUES (
  '${tag.organizationId}', 
  ${tag.companyFilterId ? `'${tag.companyFilterId}'` : 'NULL'}, 
  '${tag.investorCode}', 
  '${tag.tagName}', 
  '${tag.tagCategory}', 
  '${tag.modifiedAt}'
)
ON CONFLICT ("organizationId", "investorCode", "tagName") 
DO UPDATE SET 
  "companyFilterId" = ${tag.companyFilterId ? `'${tag.companyFilterId}'` : 'NULL'},
  "modifiedAt" = '${tag.modifiedAt}'
`
    };
  });

  // 构建响应数据 - 成功情况下errmsg保持简洁
  const result = {
    errorcode: 0,
    errmsg: "", // 简洁的成功信息
    message: `标签处理完成，共准备${tagResults.length}条标签数据${companyFilterId ? '（已关联CompanyFilter: ' + companyFilterId + '）' : '（未找到CompanyFilter配置）'}`, // 详细信息放在单独的字段
    tagResults: tagResults,
    sqlStatements: sqlStatements,
    processedCount: tagResults.length,
    organizationId: organizationId,
    stockCode: stockCode,
    companyFilterId: companyFilterId,
    // 统计信息
    stats: {
      totalTags: tagResults.length,
      ownCompanyTags: tagResults.filter(tag => tag.tagName.startsWith('持有本司')).length,
      benchmarkTags: tagResults.filter(tag => tag.tagName.startsWith('持有对标')).length
    }
  };

  return result;

} catch (error) {
  // 异常处理
  return {
    errorcode: -1,
    errmsg: `标签处理异常: ${error.message}`,
    tagResults: [],
    processedCount: 0,
    timestamp: new Date().toISOString()
  };
}
