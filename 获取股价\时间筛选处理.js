// 获取输入数据
const inputData = $input.all();

try {
  // 处理不同的输入数据格式
  let data;
  if (Array.isArray(inputData)) {
    // 如果inputData是数组，取第一个元素的json，或者直接使用inputData
    data = inputData[0]?.json || inputData;
  } else {
    data = inputData?.json || inputData;
  }
  
  // 确保data是数组
  if (!Array.isArray(data)) {
    return [{ json: { error: "输入数据格式错误，期望数组格式" } }];
  }
  
  // 提取companyCode和registerDate
  let companyCode = null;
  const registerDates = [];
  
  // 遍历输入数组，分离companyCode和registerDate
  data.forEach(item => {
    if (item.companyCode) {
      companyCode = item.companyCode;
    }
    if (item.registerDate) {
      registerDates.push(new Date(item.registerDate));
    }
  });
  
  // 验证是否有足够的数据
  if (!companyCode) {
    return [{ json: { error: "缺少companyCode" } }];
  }
  
  if (registerDates.length < 2) {
    return [{ json: { 
      companyCode: companyCode,
      stockcode: `${companyCode}.sz`,
      startdate1: "",
      enddate1: "",
      message: "registerDate数量不足2个"
    } }];
  }
  
  // 按时间降序排序，获取最新的两个时间
  registerDates.sort((a, b) => b - a);
  const latestDate = registerDates[0];
  const secondLatestDate = registerDates[1];
  
  // 计算时间差（天数）
  const timeDiff = Math.abs(latestDate - secondLatestDate);
  const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
  
  // 格式化日期为YYYYMMDD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  };
  
  // 自动补齐股票代码后缀
  const getStockCode = (code) => {
    if (code.includes('.')) {
      return code;
    }
    
    if (code.startsWith('0') || code.startsWith('3')) {
      return `${code}.sz`;
    } else if (code.startsWith('6')) {
      return `${code}.sh`;
    } else {
      return `${code}.sz`;
    }
  };
  
  const stockcode = getStockCode(companyCode);
  
  // 检查时间差是否超过30天
  let startdate1 = "";
  let enddate1 = "";
  
  if (daysDiff <= 30) {
    startdate1 = formatDate(secondLatestDate);
    enddate1 = formatDate(latestDate);
  }
  
  return [{ 
    json: { 
      companyCode: companyCode,
      stockcode: stockcode,
      startdate1: startdate1,
      enddate1: enddate1,
      daysDiff: daysDiff,
      isValid: daysDiff <= 30
    } 
  }];
  
} catch (error) {
  return [{ json: { error: `处理失败: ${error.message}` } }];
}
