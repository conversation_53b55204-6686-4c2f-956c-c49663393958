/**
 * 生成潜在投资人标签
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 为匹配的基金生成潜在投资人标签数据
 */

try {
  // 获取上游数据
  const checkResult = $('检查已存在标签').first().json;
  const matchedFunds = checkResult.matchedFunds || [];
  const organizationId = checkResult.organizationId;
  
  // 获取已存在标签的查询结果
  const existingTagsItems = $('执行标签检查查询').all() || [];
  const existingTags = existingTagsItems.map(item => item.json);
  
  if (matchedFunds.length === 0) {
    return {
      errorcode: 0,
      errmsg: "",
      message: "没有需要生成标签的基金",
      newTags: [],
      newCount: 0
    };
  }

  // 构建已存在标签的映射
  const existingTagMap = new Set();
  existingTags.forEach(tag => {
    const key = `${tag.organizationId}_${tag.investorCode}_潜在投资人`;
    existingTagMap.add(key);
  });

  // 过滤出需要新增的标签
  const newTags = [];
  const currentTime = new Date().toISOString();
  
  for (const fund of matchedFunds) {
    const key = `${organizationId}_${fund.investorCode}_潜在投资人`;
    
    // 如果标签不存在，则创建新标签
    if (!existingTagMap.has(key)) {
      const tagData = {
        id: 'cuid_' + Math.random().toString(36).substring(2, 15),
        organizationId: organizationId,
        companyFilterId: fund.companyFilterId,
        investorCode: fund.investorCode,
        tagName: "潜在投资人",
        tagCategory: "system",
        modifiedAt: currentTime,
        tagMetadata: {
          stockIndustry: fund.stockIndustry,
          matchedIndustries: fund.matchedIndustries,
          generatedAt: currentTime
        }
      };
      
      newTags.push(tagData);
    }
  }

  return {
    errorcode: 0,
    errmsg: "",
    message: `生成${newTags.length}条新的潜在投资人标签`,
    newTags: newTags,
    newCount: newTags.length,
    totalMatched: matchedFunds.length,
    existingCount: matchedFunds.length - newTags.length
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `生成标签异常: ${error.message}`,
    newTags: [],
    newCount: 0
  };
}