/**
 * 准备投资人标签SQL插入语句
 * <AUTHOR>
 * @date 2023-11-28T15:12:34.567Z
 * @description 从智能机构持股查询结果中提取投资人标签数据，并准备SQL插入语句
 * 功能说明：
 * - 获取上游节点中的投资人标签数据
 * - 检查标签数据有效性
 * - 准备批量插入SQL语句
 * - 添加时间戳和组织ID
 * 修改记录：
 * - 2023-11-28: 初始创建，实现SQL准备逻辑
 * - 2023-11-28: 修复SQL字段错误，将createdAt改为modifiedAt
 * - 2023-11-28: 添加companyFilterId字段到SQL语句中
 * - 2023-11-28: 在tagMetadata中添加reportDate字段数据
 * - 2025-07-21: 去重逻辑改为 organizationId + investorCode + companyFilterId + tagName
 */

try {
  // 获取上游节点的投资人标签数据
  const investorTags = $('智能机构持股查询（网络请求）').first().json.investorTags || [];

  // 获取组织ID和公司筛选配置ID
  const organizationId = $('成功数据格式化').item.json.organizationId || '';
  const companyFilterId = $('查询公司筛选配置').first().json.companyFilterId || null;

  // 检查数据有效性
  if (!Array.isArray(investorTags) || investorTags.length === 0) {
    return {
      success: false,
      needDatabaseUpdate: false,
      message: '没有找到有效的投资人标签数据',
      sql: '',
      newTagsCount: 0,
      newTags: [],
      timestamp: new Date().toISOString()
    };
  }

  // 获取已存在的标签数据用于去重
  const existingTagsItems = $('检查已存在的投资人标签').all() || [];
  const existingTags = existingTagsItems.map(item => item.json);
  const existingTagMap = new Map();

  // 调试信息 - 检查数据来源
  console.log('organizationId:', organizationId);
  console.log('companyFilterId:', companyFilterId);
  console.log('existingTags数量:', existingTags.length);
  console.log('investorTags数量:', investorTags.length);

  // 构建已存在标签的映射 - 使用与数据库冲突检查相同的key
  existingTags.forEach(tag => {
    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;
    existingTagMap.set(key, true);
    console.log('已存在标签key:', key);
  });

  // 过滤出需要新增的标签
  const newTags = investorTags.filter(tag => {
    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;
    const exists = existingTagMap.has(key);
    console.log('检查标签key:', key, '是否存在:', exists);
    return !exists;
  });

  if (newTags.length === 0) {
    return {
      success: false,
      needDatabaseUpdate: false,
      message: '没有找到新的投资人标签数据',
      sql: '',
      newTagsCount: 0,
      newTags: [],
      existingTagsCount: investorTags.length,
      timestamp: new Date().toISOString()
    };
  }

  // 准备SQL插入语句
  const timestamp = new Date().toISOString();
  const valueStrings = newTags.map(tag => {
    // 使用UUID生成ID
    const id = 'cuid_' + Math.random().toString(36).substring(2, 15);

    // 创建包含reportDate的tagMetadata JSON对象
    const tagMetadata = {
      reportDate: tag.reportDate || null
    };

    // 将tagMetadata转换为JSON字符串并转义单引号
    const tagMetadataJson = JSON.stringify(tagMetadata).replace(/'/g, "''");

    // 处理companyFilterId可能为null的情况
    const filterIdValue = companyFilterId ? `'${companyFilterId}'` : 'NULL';

    return `('${id}', '${tag.investorCode}', '${tag.tagName}', '${tag.tagCategory || 'system'}', '${organizationId}', ${filterIdValue}, '${tagMetadataJson}', '${timestamp}')`;
  });

  // 修正SQL语句，使用正确的字段名，并添加companyFilterId和tagMetadata
  const sql = `INSERT INTO investor_tag ("id", "investorCode", "tagName", "tagCategory", "organizationId", "companyFilterId", "tagMetadata", "modifiedAt") VALUES ${valueStrings.join(', ')} ON CONFLICT ("organizationId", "investorCode", "tagName") DO UPDATE SET "modifiedAt" = EXCLUDED."modifiedAt", "tagMetadata" = EXCLUDED."tagMetadata";`;

  return {
    success: true,
    needDatabaseUpdate: true,
    message: `发现${newTags.length}条新的投资人标签，需要执行数据库更新`,
    sql: sql,
    newTagsCount: newTags.length,
    newTags: newTags.map(tag => ({
      investorCode: tag.investorCode,
      tagName: tag.tagName,
      tagCategory: tag.tagCategory || 'system',
      reportDate: tag.reportDate
    })),
    totalTagsFromAPI: investorTags.length,
    existingTagsCount: investorTags.length - newTags.length,
    companyFilterId: companyFilterId,
    timestamp: timestamp
  };
} catch (error) {
  return {
    success: false,
    needDatabaseUpdate: false,
    error: {
      code: 'SQL_PREPARATION_ERROR',
      message: '准备SQL语句时发生错误',
      details: error.message || '未知错误',
      timestamp: new Date().toISOString()
    }
  };
}
