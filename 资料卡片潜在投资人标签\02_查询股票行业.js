/**
 * 查询股票所属行业
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 调用同花顺API查询目标股票的所属行业
 */

try { 
  // 获取access_token和companyCode
  const accessToken = $('缓存acc token').first().json.access_token
  const companyCode = $('查询公司筛选配置').first().json.companyCode
  
  if (!accessToken) {
    throw new Error('缺少access_token');
  }
  
  if (!companyCode) {
    throw new Error('缺少companyCode');
  }

  // 生成当前日期 YYYYMMDD格式
  const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

  // 配置HTTP请求
  const options = {
    url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'access_token': accessToken,
      'ifindlang': 'cn'
    },
    body: {
      codes: companyCode,
      indipara: [{
        indicator: "ths_the_new_csrc_industry_stock",
        indiparams: ["100", currentDate]
      }]
    },
    json: true,
    timeout: 30000
  };

  // 执行HTTP请求
  const response = await this.helpers.httpRequest(options);
  
  if (response.errorcode !== 0) {
    throw new Error(`API请求失败: ${response.errmsg || '未知错误'}`);
  }

  // 解析行业数据
  let stockIndustry = null;
  if (response.tables && response.tables.length > 0 && response.tables[0].table) {
    const tableData = response.tables[0].table;
    if (tableData.ths_the_new_csrc_industry_stock && tableData.ths_the_new_csrc_industry_stock.length > 0) {
      stockIndustry = tableData.ths_the_new_csrc_industry_stock[0];
    }
  }

  if (!stockIndustry) {
    throw new Error('未获取到股票行业信息');
  }

  return {
    errorcode: 0,
    errmsg: "",
    message: `成功获取股票${companyCode}的行业信息`,
    companyCode: companyCode,
    stockIndustry: stockIndustry,
    queryDate: currentDate
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `查询股票行业异常: ${error.message}`,
    companyCode: null,
    stockIndustry: null,
    queryDate: null
  };
}