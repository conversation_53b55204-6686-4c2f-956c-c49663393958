<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>基金数据分析</title>
</head>

<body>
  <div class="container">
    <!-- 上半部分：基金基本信息 -->
    <div class="fund-info-section">
      <div class="fund-header">
        <div class="fund-type">基金类型：{{ $json.investment_type || '-' }}</div>
        <div class="fund-date">成立日期：
          {{ $json.establishment_date ?
          new Date($json.establishment_date).toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day:
          '2-digit'}).replace(/\//g, '年').replace(/\//g, '月') + '日' : '-' }}</div>
        <div class="fund-index">指数名称：{{ $json.tracking_index_name || '-' }}</div>
        <div class="fund-company">基金公司：<span class="value">{{ $json.management_company_name || '-' }}</span></div>
        <div class="fund-manager">基金经理：<span class="value">{{ $json.manager_name_1st || '-' }}{{ $json.manager_name_2nd
            ? '，' + $json.manager_name_2nd : '' }}</span></div>
        <div class="fund-connection">联接基金：{{ $json.related_fund_code || '-' }}</div>
      </div>

      <div class="fund-metrics">
        <div class="metrics-row">
          <div class="metric">总资产规模：<span class="value">{{ $json.net_asset_value && $json.net_asset_value !== '-' ?
              $json.net_asset_value + '亿元' : '-' }}</span></div>
          <div class="metric">基金净值：<span class="value">{{ $json.unit_net_value || '-' }}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">基金场内规模：<span class="value">{{ $json.onMarketScale && $json.onMarketScale !== '-' ?
              $json.onMarketScale + '亿元' : '-' }}</span></div>
          <div class="metric">收盘价格：<span class="value">{{ $json.closing_price || '-' }}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">管理费用率：<span class="value">{{ $json.management_fee_rate && $json.management_fee_rate !==
              '-' ? $json.management_fee_rate + '%' : '-' }}</span></div>
          <div class="metric">收盘价涨跌幅：<span class="value"
              style="color: {{ parseFloat($json.price_change_pct || 0) >= 0 ? 'red' : 'green' }}">{{
              $json.price_change_pct && $json.price_change_pct !== '-' ? $json.price_change_pct : '-' }}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">托管费用率：<span class="value">{{ $json.custody_fee_rate && $json.custody_fee_rate !== '-' ?
              $json.custody_fee_rate + '%' : '-' }}</span></div>
          <div class="metric">成交金额：<span class="value">{{ $json.trading_volume && $json.trading_volume !== '-' ?
              ($json.trading_volume / 100000000).toFixed(2) + '亿元' : '-' }}</span></div>
        </div>
        <div class="metrics-row">
          <div class="metric">持仓换手率：<span class="value">{{ $json.turnover_rate && $json.turnover_rate !== '-' ?
              $json.turnover_rate + '%' : '-' }}</span></div>
          <div class="metric">折价率：<span class="value positive">{{ $json.premium_discount_rate &&
              $json.premium_discount_rate !== '-' ? $json.premium_discount_rate + '%' : '-' }}</span></div>
        </div>
      </div>

      <!-- 收益表现和行业配置 -->
      <div class="performance-industry">
        <div class="performance-section">
          <h3>收益率表现</h3>
          <table class="performance-table">
            <tr>
              <th>基金业绩</th>
              <th></th>
            </tr>
            <tr>
              <td>今年以来：</td>
              <td style="color: {{ parseFloat($json.ytd_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.ytd_total_return ? (parseFloat($json.ytd_total_return) >= 0 ? '+' + $json.ytd_total_return :
                $json.ytd_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近一个月：</td>
              <td style="color: {{ parseFloat($json.month_1_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.month_1_total_return ? (parseFloat($json.month_1_total_return) >= 0 ? '+' +
                $json.month_1_total_return : $json.month_1_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近三个月：</td>
              <td style="color: {{ parseFloat($json.month_3_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.month_3_total_return ? (parseFloat($json.month_3_total_return) >= 0 ? '+' +
                $json.month_3_total_return : $json.month_3_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近六个月：</td>
              <td style="color: {{ parseFloat($json.month_6_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.month_6_total_return ? (parseFloat($json.month_6_total_return) >= 0 ? '+' +
                $json.month_6_total_return : $json.month_6_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近1年：</td>
              <td style="color: {{ parseFloat($json.year_1_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.year_1_total_return ? (parseFloat($json.year_1_total_return) >= 0 ? '+' +
                $json.year_1_total_return : $json.year_1_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近2年：</td>
              <td style="color: {{ parseFloat($json.year_2_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.year_2_total_return ? (parseFloat($json.year_2_total_return) >= 0 ? '+' +
                $json.year_2_total_return : $json.year_2_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近3年：</td>
              <td style="color: {{ parseFloat($json.year_3_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.year_3_total_return ? (parseFloat($json.year_3_total_return) >= 0 ? '+' +
                $json.year_3_total_return : $json.year_3_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近5年：</td>
              <td style="color: {{ parseFloat($json.year_5_total_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.year_5_total_return ? (parseFloat($json.year_5_total_return) >= 0 ? '+' +
                $json.year_5_total_return : $json.year_5_total_return) + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>成立以来：</td>
              <td style="color: {{ parseFloat($json.since_inception_return || 0) >= 0 ? 'red' : 'green' }}">
                {{ $json.since_inception_return ? (parseFloat($json.since_inception_return) >= 0 ? '+' +
                $json.since_inception_return : $json.since_inception_return) + '%' : '-' }}
              </td>
            </tr>
          </table>
        </div>

        <div class="industry-section">
          <h3>行业配置</h3>
          <table class="industry-table">
            <tr>
              <th>行业名称</th>
              <th>占净值比</th>
            </tr>
            {{ [$json.top_industry_1, $json.top_industry_2, $json.top_industry_3, $json.top_industry_4,
            $json.top_industry_5,
            $json.top_industry_6, $json.top_industry_7, $json.top_industry_8, $json.top_industry_9,
            $json.top_industry_10]
            .map((industry, index) => {
            const ratios = [$json.industry_ratio_1, $json.industry_ratio_2, $json.industry_ratio_3,
            $json.industry_ratio_4, $json.industry_ratio_5,
            $json.industry_ratio_6, $json.industry_ratio_7, $json.industry_ratio_8, $json.industry_ratio_9,
            $json.industry_ratio_10];
            return industry && industry !== '-' && industry !== null ? `
            <tr>
              <td>${industry}</td>
              <td>${ratios[index] ? ratios[index] + '%' : '-'}</td>
            </tr>
            ` : '';
            }).join('') }}
          </table>
        </div>
      </div>

      <!-- 重仓股票和重仓债券 -->
      <div class="holdings-section">
        <!-- 重仓股票 -->
        <h3>重仓股票（TOP10）</h3>
        <table class="holdings-table">
          <tr>
            <th>证券代码</th>
            <th>证券简称</th>
            <th>持仓市值(元)</th>
            <th>占基金净值比</th>
            <th>持仓数量</th>
          </tr>
          {{ ($json.top_stocks || [])
          .filter(stock => stock && stock.top_stock_code && stock.top_stock_name && stock.top_stock_code !== '-' &&
          stock.top_stock_name !== '-')
          .slice(0, 10)
          .map(stock => `
          <tr>
            <td>${stock.top_stock_code}</td>
            <td>${stock.top_stock_name}</td>
            <td>${stock.holding_market_value || '-'}</td>
            <td>${stock.fund_net_value_ratio ? stock.fund_net_value_ratio + '%' : '-'}</td>
            <td>${stock.holding_quantity || '-'}</td>
          </tr>
          `).join('') }}
        </table>

        <!-- 重仓债券 -->
        <h3>重仓债券（TOP5）</h3>
        <table class="holdings-table">
          <tr>
            <th>债券代码</th>
            <th>债券简称</th>
            <th>持仓市值(元)</th>
            <th>占基金净值比</th>
            <th>持仓数量</th>
          </tr>
          {{ ($json.top_bonds || [])
          .filter(bond => bond && bond.top_bond_code && bond.top_bond_name && bond.top_bond_code !== '-' &&
          bond.top_bond_name !== '-')
          .slice(0, 5)
          .map(bond => `
          <tr>
            <td>${bond.top_bond_code}</td>
            <td>${bond.top_bond_name}</td>
            <td>${bond.holding_market_value || '-'}</td>
            <td>${bond.fund_net_value_ratio ? bond.fund_net_value_ratio + '%' : '-'}</td>
            <td>${bond.holding_quantity || '-'}</td>
          </tr>
          `).join('') }}
        </table>
      </div>
    </div>

    <!-- 下半部分：基金经理信息 -->
    <div class="manager-section">
      <h3 class="section-title">基金经理-现任</h3>

      <div class="manager-profile">
        <div class="manager-name-section">
          <div class="manager-name">
            <span>{{ $json.manager_name || '-' }}</span>
            <span class="gender">{{ $json.gender || '-' }}</span>

            <span class="education">{{ $json.education || '-' }}</span>
          </div>

          <div class="manager-content">
            <div class="manager-stats">
              <div class="stats-grid">
                <div class="stat-label">投资经理年限</div>
                <div class="stat-value">{{ $json.management_years ? $json.management_years + ' 年' : '-' }}</div>

                <div class="stat-label">历任管理基金数</div>
                <div class="stat-value">{{ $json.historical_fund_count ? $json.historical_fund_count + ' 只' : '-' }}
                </div>

                <div class="stat-label">在任基金总规模</div>
                <div class="stat-value">{{ $json.total_fund_scale ? $json.total_fund_scale + ' 元' : '-' }}</div>

                <div class="stat-label">现任公司年限</div>
                <div class="stat-value">{{ $json.current_company_years ? $json.current_company_years + ' 年' : '-' }}
                </div>

                <div class="stat-label">在任管理基金数</div>
                <div class="stat-value">{{ $json.current_fund_count ? $json.current_fund_count + ' 只' : '-' }}</div>

                <div class="stat-label">获奖数</div>
                <div class="stat-value">{{ $json.award_count ? $json.award_count + ' 个' : '-' }}</div>
              </div>
            </div>

            <div class="manager-bio-section">
              <h4 class="bio-title">基金经理简介:</h4>
              <div class="manager-bio">
                <p>{{ $json.resume || '-' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 收益率表现和前十大管理基金 -->
      <div class="performance-funds">
        <div class="manager-performance">
          <h3>收益率表现</h3>
          <table class="performance-table">
            <tr>
              <th>任职间收益率</th>
              <th></th>
            </tr>
            <tr>
              <td>今年以来</td>
              <td
                class="{{ $json.ytd_return_percent && parseFloat($json.ytd_return_percent) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.ytd_return_percent ? $json.ytd_return_percent + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近1个月</td>
              <td
                class="{{ $json.recent_one_month_return && parseFloat($json.recent_one_month_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_one_month_return ? $json.recent_one_month_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近3个月</td>
              <td
                class="{{ $json.recent_three_month_return && parseFloat($json.recent_three_month_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_three_month_return ? $json.recent_three_month_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近6个月</td>
              <td
                class="{{ $json.recent_six_month_return && parseFloat($json.recent_six_month_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_six_month_return ? $json.recent_six_month_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近1年</td>
              <td
                class="{{ $json.recent_one_year_return && parseFloat($json.recent_one_year_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_one_year_return ? $json.recent_one_year_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近2年</td>
              <td
                class="{{ $json.recent_two_year_return && parseFloat($json.recent_two_year_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_two_year_return ? $json.recent_two_year_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>近3年</td>
              <td
                class="{{ $json.recent_three_year_return && parseFloat($json.recent_three_year_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.recent_three_year_return ? $json.recent_three_year_return + '%' : '-' }}
              </td>
            </tr>
            <tr>
              <td>管理基金以来年化收益率</td>
              <td
                class="{{ $json.tenure_annualized_return && parseFloat($json.tenure_annualized_return) >= 0 ? 'positive' : 'negative' }}">
                {{ $json.tenure_annualized_return ? $json.tenure_annualized_return + '%' : '-' }}
              </td>
            </tr>
          </table>
          <div class="calculation-note">
            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。
          </div>
        </div>

        <div class="top-funds">
          <h3>前十大管理基金</h3>
          <table class="funds-table">
            <tr>
              <th>基金名称</th>
              <th>总资产规模</th>
            </tr>
            {{ ($json.top_funds || [])
            .filter(fund => fund && fund.fund_name && fund.fund_name !== '-' && fund.fund_name.trim() !== '')
            .slice(0, 10)
            .map(fund => `
            <tr>
              <td><a href="#" class="fund-link">${fund.fund_name}</a></td>
              <td>${fund && fund.fund_scale ? fund.fund_scale + '亿元' : '-'}</td>
            </tr>
            `).join('') }}
          </table>
        </div>
      </div>

      <!-- 基金公司信息 -->
      <div class="company-section">
        <h3>{{ $json.fund_company_name || '基金公司' }}</h3>
        <div class="company-info-grid">
          <!-- 基本信息 -->
          <div class="company-info-block">
            <h4 class="section-subtitle">基本资料</h4>
            <div class="company-content company-basic-info">
              <table class="inner-table compact-table">
                <tr>
                  <td class="info-label">成立日期</td>
                  <td class="info-value text-overflow">{{ $json.establish_date ?
                    $json.establish_date.toString().replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') : '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">注册资本(万)</td>
                  <td class="info-value text-overflow">{{ $json.registered_capital || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">总经理</td>
                  <td class="info-value text-overflow">{{ $json.general_manager || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">管理资产规模</td>
                  <td class="info-value text-overflow">{{ $json.asset_management_scale ?
                    (parseFloat($json.asset_management_scale) / 100000000).toFixed(2) + '亿元' : '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">旗下基金数量</td>
                  <td class="info-value text-overflow">{{ $json.total_funds_count || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">基金经理人数</td>
                  <td class="info-value text-overflow">{{ $json.fund_manager_count || '-' }}</td>
                </tr>
              </table>
            </div>
          </div>

          <!-- 股东信息 -->
          <div class="company-info-block">
            <h4 class="section-subtitle">股东列表</h4>
            <div class="company-content shareholders-content">
              <table class="inner-table shareholders-table">
                <tr>
                  <th class="shareholder-name">股东名称</th>
                  <th class="shareholder-ratio">持股比例</th>
                </tr>
                {{ ($json.shareholders || [
                {name: '开发中', ratio: '-'},
                ]).map(shareholder => `
                <tr>
                  <td class="shareholder-name text-overflow">${shareholder.name}</td>
                  <td class="shareholder-ratio text-overflow">${shareholder.ratio}</td>
                </tr>
                `).join('') }}
              </table>
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="company-info-block">
            <h4 class="section-subtitle">联系方式</h4>
            <div class="company-content company-contact-info">
              <table class="inner-table compact-table">
                <tr>
                  <td class="info-label">电话</td>
                  <td class="info-value text-overflow">{{ $json.phone || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">传真</td>
                  <td class="info-value text-overflow">{{ $json.fax || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label">网址</td>
                  <td class="info-value text-overflow"><a
                      href="{{ $json.website ? 'http://' + $json.website.replace(/^https?:\/\//, '') : '#' }}"
                      target="_blank" class="website-link">{{ $json.website || '-' }}</a></td>
                </tr>
                <tr>
                  <td class="info-label">办公地址</td>
                  <td class="info-value text-overflow">{{ $json.office_address || '-' }}</td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* 全局样式 */
    body {
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      margin: 0;
      padding: 0;
      color: #333;
      line-height: 1.5;
    }

    .container {
      margin: 0 auto;
      padding: 20px;
    }

    h3 {
      color: #1e293b;
      font-size: 18px;
      font-weight: 600;
      margin: 16px 0;
      padding-left: 8px;
      border-left: 4px solid #0ea5e9;
    }

    /* 基金信息部分 */
    .fund-header {
      margin-bottom: 16px;
    }

    .fund-type,
    .fund-date,
    .fund-index,
    .fund-company,
    .fund-manager,
    .fund-connection,
    .metric {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .fund-index {
      font-weight: normal;
    }

    .fund-connection {
      color: #555;
      margin-bottom: 16px;
    }

    .fund-metrics {
      margin-bottom: 20px;
    }

    .metrics-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .metric {
      flex: 1;
      font-size: 14px;
    }

    .value {
      font-weight: 600;
    }

    .positive {
      color: #dc2626;
    }

    .negative {
      color: #16a34a;
    }

    /* 表格样式 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      font-size: 14px;
    }

    th,
    td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      font-weight: 600;
    }

    /* 布局 */
    .performance-industry {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .performance-section,
    .industry-section {
      flex: 1;
    }

    /* 标签页导航样式 */
    .tab-navigation {
      display: flex;
      margin-bottom: 16px;
      border-bottom: 2px solid #e2e8f0;
    }

    .tab-button {
      padding: 12px 24px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #64748b;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .tab-button:hover {
      color: #0ea5e9;
      background-color: #f8fafc;
    }

    .tab-button.active {
      color: #0ea5e9;
      border-bottom-color: #0ea5e9;
      font-weight: 600;
    }

    /* 标签内容样式 */
    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    /* 持仓表格样式优化 */
    .holdings-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 0;
    }

    .holdings-table th {
      background-color: #f8fafc;
      font-weight: 600;
      color: #1e293b;
    }

    /* 基金经理部分 */
    .manager-section {
      margin-top: 30px;
      border-top: 1px solid #e2e8f0;
      padding-top: 20px;
    }

    .section-title {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .manager-profile {
      margin-bottom: 20px;
    }

    .manager-name-section {
      width: 100%;
    }

    .manager-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
    }

    .manager-content {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }

    .manager-stats {
      flex: 1;
      min-width: 300px;
    }

    .manager-bio-section {
      flex: 1.5;
    }

    .bio-title {
      margin: 0 0 8px 0;
      font-size: 15px;
      font-weight: 600;
      color: #1e293b;
    }

    .manager-bio {
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #f8fafc;
      max-height: 160px;
      overflow-y: auto;
    }

    .manager-bio p {
      margin: 0;
      font-size: 13px;
      line-height: 1.4;
      color: #475569;
      text-align: justify;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 150px auto;
      gap: 8px;
      margin-bottom: 16px;
    }

    .stat-label {
      color: #64748b;
    }

    .stat-value {
      font-weight: 600;
    }

    .performance-funds {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .manager-performance,
    .top-funds {
      flex: 1;
    }

    .calculation-note {
      font-size: 12px;
      color: #64748b;
      margin-top: 10px;
      padding: 8px;
      background-color: #f1f5f9;
      border-radius: 4px;
    }

    .fund-link {
      color: #0ea5e9;
      text-decoration: none;
    }

    .fund-link:hover {
      text-decoration: underline;
    }

    /* 基金公司信息样式 */
    .company-section {
      margin-top: 20px;
    }

    .company-info-grid {
      display: grid;
      grid-template-columns: 1fr 1.2fr 1fr;
      gap: 15px;
      margin-top: 15px;
      align-items: start;
    }

    .company-info-block {
      display: flex;
      flex-direction: column;
      min-height: 280px;
      height: auto;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      background-color: #ffffff;
    }

    .section-subtitle {
      color: #1e293b;
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      padding: 12px 15px;
      background-color: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      border-radius: 6px 6px 0 0;
    }

    .company-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;
    }

    .shareholders-content {
      max-height: 230px;
    }

    .inner-table {
      width: 100%;
      border-collapse: collapse;
      margin: 0;
    }

    .inner-table td {
      padding: 10px 15px;
      border-bottom: 1px solid #f1f5f9;
      border-left: none;
      border-right: none;
      font-size: 13px;
    }

    .inner-table tr:last-child td {
      border-bottom: none;
    }

    .info-label {
      color: #64748b;
      width: 45%;
      font-weight: 500;
    }

    .info-value {
      font-weight: 500;
      color: #1e293b;
    }

    /* 基金公司和基金经理的value显示为蓝色 */
    .fund-company .value,
    .fund-manager .value {
      color: #0ea5e9;
    }

    .shareholders-table {
      width: 100%;
    }

    .shareholder-name {
      text-align: left;
      color: #475569;
      width: 70%;
      box-shadow: none;
      text-shadow: none;
    }

    .shareholder-ratio {
      text-align: right;
      font-weight: 600;
      color: #1e293b;
      width: 30%;
      box-shadow: none;
      text-shadow: none;
    }

    .shareholders-table th {
      background-color: #ffffff;
      border-bottom: 1px solid #e2e8f0;
      padding: 10px 15px;
      font-size: 14px;
      color: #1e293b;
      font-weight: 600;
      position: sticky;
      top: 0;
      z-index: 1;
      box-shadow: none;
      text-shadow: none;
    }

    .website-link {
      color: #0ea5e9;
      text-decoration: none;
      word-break: break-all;
      line-height: 1.4;
    }

    .website-link:hover {
      text-decoration: underline;
    }

    /* 滚动条样式 */
    .company-content::-webkit-scrollbar {
      width: 6px;
    }

    .company-content::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }

    .company-content::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
    }

    .company-content::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }

    /* 移除重仓债券表格底部边框 */
    .holdings-section .holdings-table:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .holdings-section .holdings-table:last-child tr:last-child td {
      border-bottom: none;
    }

    /* 基本资料和联系方式取消滚动 */
    .company-basic-info,
    .company-contact-info {
      overflow-y: visible !important;
    }

    /* 紧凑表格样式 */
    .compact-table {
      table-layout: fixed;
      width: 100%;
    }

    .compact-table td {
      padding: 6px 15px !important;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      vertical-align: top;
    }

    .compact-table .info-label {
      width: 35% !important;
      font-weight: 500;
      color: #64748b;
    }

    .compact-table .info-value {
      width: 65% !important;
      font-weight: 500;
      color: #1e293b;
      line-height: 1.4;
    }

    /* 移除text-overflow类的截断效果 */
    .text-overflow {
      overflow: visible !important;
      text-overflow: unset !important;
      white-space: normal !important;
    }
  </style>
</body>

</html>