/**
 * 通用参数验证器 - 支持多种数据类型的灵活验证
 * <AUTHOR>
 * @date 2025-07-04T15:22:15.935Z
 * 修改记录：
 * - 设计通用验证规则配置
 * - 支持string、number、array、object、boolean等类型
 * - 支持可选参数和默认值
 * - 支持自定义验证函数
 * - 极简配置，高度复用
 */

try {
  // 安全的数据获取
  const inputData = $input.all();
  if (!inputData || inputData.length === 0) {
    return {
      validationResult: 'error',
      error: {
        code: 'NO_INPUT_DATA',
        message: '未接收到任何输入数据',
        field: 'input',
        value: null,
        timestamp: new Date().toISOString(),
        type: 'SYSTEM_ERROR'
      }
    };
  }

  const jsonData = inputData[0]?.json;
  if (!jsonData || typeof jsonData !== 'object') {
    return {
      validationResult: 'error',
      error: {
        code: 'INVALID_INPUT_FORMAT',
        message: '输入数据格式无效，期望JSON对象',
        field: 'input',
        value: jsonData,
        timestamp: new Date().toISOString(),
        type: 'SYSTEM_ERROR'
      }
    };
  }

  // 通用验证器函数
  const validators = {
    string: (val, options = {}) => {
      if (typeof val !== 'string') return false;
      if (options.minLength && val.length < options.minLength) return false;
      if (options.maxLength && val.length > options.maxLength) return false;
      if (options.pattern && !options.pattern.test(val)) return false;
      if (options.notEmpty && val.trim() === '') return false;
      return true;
    },
    
    number: (val, options = {}) => {
      const num = Number(val);
      if (isNaN(num)) return false;
      if (options.min !== undefined && num < options.min) return false;
      if (options.max !== undefined && num > options.max) return false;
      if (options.integer && !Number.isInteger(num)) return false;
      return true;
    },
    
    array: (val, options = {}) => {
      if (!Array.isArray(val)) return false;
      if (options.minLength && val.length < options.minLength) return false;
      if (options.maxLength && val.length > options.maxLength) return false;
      if (options.itemType) {
        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));
      }
      return true;
    },
    
    object: (val, options = {}) => {
      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;
      if (options.requiredKeys) {
        return options.requiredKeys.every(key => key in val);
      }
      return true;
    },
    
    boolean: (val) => {
      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;
    },
    
    email: (val) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return typeof val === 'string' && emailRegex.test(val);
    },
    
    url: (val) => {
      try {
        new URL(val);
        return true;
      } catch {
        return false;
      }
    }
  };

  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥
  const validationConfig = [
    {
      field: 'fundcode',
      type: 'string',
      required: true,
      options: { 
        notEmpty: true, 
        minLength: 1,
        pattern: /^[0-9]{6}\.(OF|SH|SZ)$/i, // 基金代码格式：6位数字.OF/SH/SZ
        customValidator: (val) => {
          // 基金代码格式验证
          const fundCodePattern = /^[0-9]{6}\.(OF|SH|SZ)$/i;
          return fundCodePattern.test(val);
        }
      },
      defaultValue: ""
    },
    {
      field: 'organizationId',
      type: 'string', 
      required: false,
      options: { notEmpty: true, minLength: 1 },
      defaultValue: ""
    }
  ];

  // 通用验证执行器
  const processedData = {
    validationResult: 'success',
    timestamp: jsonData.timestamp || new Date().toISOString()
  };

  for (const config of validationConfig) {
    const { field, type, required, options = {}, defaultValue } = config;
    let value = jsonData[field];

    // 处理缺失值
    if (value === undefined || value === null || value === '') {
      if (required) {
        return {
          validationResult: 'error',
          error: {
            code: `MISSING_${field.toUpperCase()}`,
            message: `缺少必需参数：${field}。请提供${field}参数。`,
            field: field,
            value: value,
            timestamp: processedData.timestamp,
            type: 'VALIDATION_ERROR'
          }
        };
      } else {
        // 使用默认值
        processedData[field] = defaultValue;
        continue;
      }
    }

    // 类型转换处理
    if (type === 'number' && typeof value === 'string') {
      value = Number(value);
    } else if (type === 'boolean') {
      if (typeof value === 'string') {
        value = value.toLowerCase() === 'true' || value === '1';
      } else if (typeof value === 'number') {
        value = value === 1;
      }
    }

    // 执行验证
    const validator = validators[type];
    if (!validator) {
      return {
        validationResult: 'error',
        error: {
          code: 'UNSUPPORTED_TYPE',
          message: `不支持的参数类型：${type}`,
          field: field,
          value: value,
          timestamp: processedData.timestamp,
          type: 'SYSTEM_ERROR'
        }
      };
    }

    const isValid = validator(value, options);
    if (!isValid) {
      // 针对fundcode提供更明确的错误信息
      if (field === 'fundcode') {
        return {
          validationResult: 'error',
          error: {
            code: 'INVALID_FUND_CODE_FORMAT',
            message: '基金代码格式不符合规范。正确格式：6位数字.OF/SH/SZ（如：000001.OF）',
            field: field,
            value: value,
            timestamp: processedData.timestamp,
            type: 'VALIDATION_ERROR'
          }
        };
      }
      
      return {
        validationResult: 'error',
        error: {
          code: `INVALID_${field.toUpperCase()}`,
          message: `${field}参数验证失败。期望类型：${type}`,
          field: field,
          value: value,
          timestamp: processedData.timestamp,
          type: 'VALIDATION_ERROR'
        }
      };
    }

    // 自定义验证器检查
    if (options.customValidator && !options.customValidator(value)) {
      if (field === 'fundcode') {
        return {
          validationResult: 'error',
          error: {
            code: 'INVALID_FUND_CODE_FORMAT',
            message: '基金代码格式不符合规范。正确格式：6位数字.OF/SH/SZ（如：000001.OF）',
            field: field,
            value: value,
            timestamp: processedData.timestamp,
            type: 'VALIDATION_ERROR'
          }
        };
      }
    }

    // 验证通过，保存处理后的值
    processedData[field] = value;
  }

  return processedData;

} catch (error) {
  // 全局异常捕获
  return {
    validationResult: 'error',
    error: {
      code: 'UNEXPECTED_ERROR',
      message: '系统发生未预期的错误',
      field: 'system',
      value: error.message || '未知错误',
      timestamp: new Date().toISOString(),
      type: 'SYSTEM_ERROR'
    }
  };
}
