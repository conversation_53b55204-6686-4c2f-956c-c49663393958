/**
 * 筛选持有对标的基金
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 从输入数据中筛选出tagName为"持有对标"的基金记录
 */

try {
  // 获取输入数据
  const inputData = $input.all();
  
  if (!inputData || inputData.length === 0) {
    return {
      errorcode: -1,
      errmsg: "未接收到输入数据",
      benchmarkFunds: [],
      processedCount: 0
    };
  }

  // 解析JSON数据 - 修复n8n格式解析
  let fundData = [];
  
  // 检查是否是n8n格式（每个元素有json属性）
  if (inputData.length > 0 && inputData[0].json) {
    // n8n格式：每个item都有json属性，直接提取所有json数据
    fundData = inputData.map(item => item.json).filter(item => item && item.id && item.tagName);
  } else if (inputData.length > 0 && inputData[0].id && inputData[0].tagName) {
    // 直接数组格式
    fundData = inputData;
  } else {
    // 其他格式尝试解析
    for (const input of inputData) {
      if (input.json && Array.isArray(input.json)) {
        fundData = input.json;
        break;
      } else if (Array.isArray(input)) {
        fundData = input;
        break;
      }
    }
  }

  console.log('解析后的fundData长度:', fundData.length);
  console.log('前3条数据示例:', fundData.slice(0, 3));

  if (!Array.isArray(fundData) || fundData.length === 0) {
    return {
      errorcode: -1,
      errmsg: "未找到有效的基金数据",
      benchmarkFunds: [],
      processedCount: 0,
      debug: {
        inputDataLength: inputData.length,
        firstInputType: typeof inputData[0],
        firstInputKeys: inputData[0] ? Object.keys(inputData[0]) : []
      }
    };
  }

  // 筛选持有对标的基金
  const benchmarkFunds = fundData.filter(fund => {
    const hasTagName = fund && fund.tagName;
    const isBenchmark = hasTagName && fund.tagName.startsWith('持有对标(');
    
    console.log(`基金 ${fund?.investorCode}: tagName="${fund?.tagName}", 是否对标=${isBenchmark}`);
    
    return isBenchmark;
  });

  console.log('筛选出的持有对标基金数量:', benchmarkFunds.length);
  console.log('筛选出的持有对标基金:', benchmarkFunds);

  // 去重处理 - 基于investorCode
  const uniqueFunds = [];
  const seenCodes = new Set();
  
  for (const fund of benchmarkFunds) {
    if (fund.investorCode && !seenCodes.has(fund.investorCode)) {
      seenCodes.add(fund.investorCode);
      uniqueFunds.push(fund);
    }
  }

  return {
    errorcode: 0,
    errmsg: "",
    message: `筛选出${uniqueFunds.length}只持有对标的基金`,
    benchmarkFunds: uniqueFunds,
    processedCount: uniqueFunds.length,
    totalInputCount: fundData.length
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `筛选基金数据异常: ${error.message}`,
    benchmarkFunds: [],
    processedCount: 0,
    debug: {
      error: error.message,
      stack: error.stack
    }
  };
}


