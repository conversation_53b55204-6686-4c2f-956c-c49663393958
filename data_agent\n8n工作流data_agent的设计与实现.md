# Data Agent 智能代理工作流设计与实现

**作者**: jason
**创建时间**: 2025-08-01 14:51:59
**版本**: v2.0
**修改记录**: 2025-08-01 - 重构为简化的agent机器人代理工作流

## 项目概述

Data Agent是一个基于n8n的智能代理机器人工作流系统，专门用于自动化数据查询、处理和响应。该系统通过简化的节点设计，实现了智能对话、数据获取和自动回复功能。

## 系统架构

### 核心组件

1. **输入接收层** - 接收用户查询请求和指令
2. **智能解析层** - 解析用户意图和提取关键参数
3. **数据获取层** - 根据解析结果获取相关数据
4. **响应生成层** - 生成智能化的回复内容
5. **输出发送层** - 将处理结果返回给用户

### 技术栈

- **工作流引擎**: n8n
- **数据接口**: RESTful API
- **编程语言**: JavaScript
- **数据格式**: JSON
- **通信协议**: HTTP/HTTPS

## Agent工作流设计模式

### 1. 输入接收节点 (Input Receiver)

```javascript
/**
 * Webhook输入节点配置
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 简化为agent输入接口
 * @description 接收用户查询请求的标准化接口
 */
{
  "type": "webhook",
  "parameters": {
    "httpMethod": "POST",
    "path": "/agent/query",
    "responseMode": "responseNode",
    "options": {
      "rawBody": true
    }
  }
}
```

### 2. 意图识别节点 (Intent Recognition)

```javascript
/**
 * 用户意图识别逻辑
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增意图识别功能
 * @description 解析用户查询意图并提取关键参数
 */
const recognizeIntent = (userQuery) => {
  const query = userQuery.toLowerCase().trim();

  // 定义意图模式
  const intentPatterns = {
    'fund_query': ['基金', '查询基金', '基金信息', '基金代码'],
    'manager_query': ['基金经理', '经理信息', '管理人'],
    'data_analysis': ['分析', '数据分析', '统计'],
    'help': ['帮助', 'help', '使用说明']
  };

  // 匹配意图
  for (const [intent, patterns] of Object.entries(intentPatterns)) {
    for (const pattern of patterns) {
      if (query.includes(pattern)) {
        return {
          intent: intent,
          confidence: 0.8,
          query: userQuery,
          timestamp: new Date().toISOString()
        };
      }
    }
  }

  return {
    intent: 'unknown',
    confidence: 0.1,
    query: userQuery,
    timestamp: new Date().toISOString()
  };
};
```

### 3. 参数提取节点 (Parameter Extraction)

```javascript
/**
 * 参数提取逻辑
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增参数提取功能
 * @description 从用户查询中提取关键参数
 */
const extractParameters = (query, intent) => {
  const params = {};

  // 基金代码提取
  const fundCodePattern = /[0-9]{6}/g;
  const fundCodes = query.match(fundCodePattern);
  if (fundCodes) {
    params.fundCode = fundCodes[0];
  }

  // 基金名称提取
  const fundNamePattern = /([A-Za-z\u4e00-\u9fa5]+基金)/g;
  const fundNames = query.match(fundNamePattern);
  if (fundNames) {
    params.fundName = fundNames[0];
  }

  return {
    intent: intent,
    parameters: params,
    originalQuery: query
  };
};
```

### 4. 数据查询节点 (Data Query)

```javascript
/**
 * 智能数据查询逻辑
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 简化为智能查询功能
 * @description 根据意图和参数执行相应的数据查询
 */
const queryData = async (intent, parameters) => {
  try {
    let result = {};

    switch (intent) {
      case 'fund_query':
        if (parameters.fundCode) {
          result = await queryFundByCode(parameters.fundCode);
        } else if (parameters.fundName) {
          result = await queryFundByName(parameters.fundName);
        } else {
          result = { error: '请提供基金代码或基金名称' };
        }
        break;

      case 'manager_query':
        result = await queryManagerInfo(parameters);
        break;

      case 'help':
        result = {
          message: '我可以帮您查询基金信息、基金经理信息等。请告诉我您需要查询什么？',
          examples: [
            '查询基金 000001',
            '基金经理信息',
            '分析数据'
          ]
        };
        break;

      default:
        result = {
          error: '抱歉，我不理解您的请求。请尝试说"帮助"获取使用说明。'
        };
    }

    return {
      intent: intent,
      parameters: parameters,
      result: result,
      timestamp: new Date().toISOString(),
      status: 'success'
    };

  } catch (error) {
    return {
      intent: intent,
      parameters: parameters,
      error: error.message,
      timestamp: new Date().toISOString(),
      status: 'error'
    };
  }
};

/**
 * 基金代码查询函数
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @param {string} fundCode - 基金代码
 * @returns {Object} 基金信息
 */
const queryFundByCode = async (fundCode) => {
  // 模拟API调用
  return {
    fundCode: fundCode,
    fundName: `基金${fundCode}`,
    manager: '示例基金经理',
    scale: '10.5亿',
    type: '股票型基金'
  };
};
```

### 5. 响应生成节点 (Response Generator)

```javascript
/**
 * 智能响应生成逻辑
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增响应生成功能
 * @description 根据查询结果生成用户友好的回复
 */
const generateResponse = (queryResult) => {
  const { intent, result, error, status } = queryResult;

  if (status === 'error') {
    return {
      success: false,
      message: error || '查询过程中发生错误',
      timestamp: new Date().toISOString()
    };
  }

  let message = '';

  switch (intent) {
    case 'fund_query':
      if (result.fundName) {
        message = `基金信息查询结果：\n` +
                 `基金代码：${result.fundCode}\n` +
                 `基金名称：${result.fundName}\n` +
                 `基金经理：${result.manager}\n` +
                 `基金规模：${result.scale}\n` +
                 `基金类型：${result.type}`;
      } else {
        message = '未找到相关基金信息';
      }
      break;

    case 'help':
      message = result.message + '\n\n示例：\n' + result.examples.join('\n');
      break;

    default:
      message = result.message || '查询完成';
  }

  return {
    success: true,
    message: message,
    data: result,
    timestamp: new Date().toISOString()
  };
};
```

## Agent工作流具体实现

### 1. 智能查询代理工作流

#### 节点配置

1. **Webhook输入**: 接收用户查询请求
2. **意图识别**: 解析用户查询意图
3. **参数提取**: 提取查询关键参数
4. **条件分支**: 根据意图路由到不同处理逻辑
5. **数据查询**: 执行相应的数据查询操作
6. **响应生成**: 生成用户友好的回复
7. **结果返回**: 返回处理结果给用户

#### 工作流节点详细配置

```javascript
/**
 * 完整的Agent工作流节点配置
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 重构为Agent工作流
 * @description 智能代理机器人的完整工作流配置
 */

// 1. Webhook输入节点
const webhookNode = {
  "id": "webhook-input",
  "name": "用户查询输入",
  "type": "n8n-nodes-base.webhook",
  "typeVersion": 1,
  "position": [240, 300],
  "parameters": {
    "httpMethod": "POST",
    "path": "agent-query",
    "responseMode": "responseNode",
    "options": {}
  }
};

// 2. 意图识别节点
const intentRecognitionNode = {
  "id": "intent-recognition",
  "name": "意图识别",
  "type": "n8n-nodes-base.code",
  "typeVersion": 2,
  "position": [460, 300],
  "parameters": {
    "mode": "runOnceForAllItems",
    "jsCode": `
      // 获取用户输入
      const userQuery = $input.all()[0].json.body.query || $input.all()[0].json.query || '';

      // 意图识别逻辑
      const recognizeIntent = (query) => {
        const q = query.toLowerCase().trim();

        if (q.includes('基金') || q.includes('fund')) {
          return 'fund_query';
        } else if (q.includes('经理') || q.includes('manager')) {
          return 'manager_query';
        } else if (q.includes('帮助') || q.includes('help')) {
          return 'help';
        } else {
          return 'unknown';
        }
      };

      const intent = recognizeIntent(userQuery);

      return [{
        json: {
          originalQuery: userQuery,
          intent: intent,
          timestamp: new Date().toISOString()
        }
      }];
    `
  }
};

// 3. 参数提取节点
const parameterExtractionNode = {
  "id": "parameter-extraction",
  "name": "参数提取",
  "type": "n8n-nodes-base.code",
  "typeVersion": 2,
  "position": [680, 300],
  "parameters": {
    "mode": "runOnceForAllItems",
    "jsCode": `
      const data = $input.all()[0].json;
      const query = data.originalQuery;
      const intent = data.intent;

      // 参数提取逻辑
      const extractParams = (query, intent) => {
        const params = {};

        // 提取基金代码 (6位数字)
        const fundCodeMatch = query.match(/\\b\\d{6}\\b/);
        if (fundCodeMatch) {
          params.fundCode = fundCodeMatch[0];
        }

        // 提取基金名称
        const fundNameMatch = query.match(/([\\u4e00-\\u9fa5A-Za-z]+基金)/);
        if (fundNameMatch) {
          params.fundName = fundNameMatch[1];
        }

        return params;
      };

      const parameters = extractParams(query, intent);

      return [{
        json: {
          ...data,
          parameters: parameters
        }
      }];
    `
  }
};
```

### 2. 条件分支和响应处理

#### 条件分支节点配置

```javascript
/**
 * 意图路由条件分支
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 简化为意图路由
 * @description 根据识别的意图路由到不同的处理节点
 */
const intentRouterNode = {
  "id": "intent-router",
  "name": "意图路由",
  "type": "n8n-nodes-base.if",
  "typeVersion": 2,
  "position": [900, 300],
  "parameters": {
    "conditions": {
      "options": {
        "caseSensitive": true,
        "leftValue": "",
        "typeValidation": "strict"
      },
      "conditions": [
        {
          "id": "fund-query-condition",
          "leftValue": "={{ $json.intent }}",
          "rightValue": "fund_query",
          "operator": {
            "type": "string",
            "operation": "equals"
          }
        }
      ],
      "combinator": "and"
    }
  }
};
```

#### 响应生成逻辑

```javascript
/**
 * 智能响应生成算法
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增响应生成算法
 * @description 根据查询结果生成用户友好的回复
 */
const generateSmartResponse = (intent, result, queryType) => {
  const responseTemplates = {
    fund_data: {
      success: `📊 基金信息查询结果：\n\n🏷️ 基金代码：{{ fundCode }}\n📈 基金名称：{{ fundName }}\n👤 基金经理：{{ manager }}\n💰 基金规模：{{ scale }}\n📋 基金类型：{{ type }}\n💵 净值：{{ netValue }}\n📊 日涨跌：{{ dayChange }}`,
      error: '抱歉，未找到相关基金信息，请检查基金代码或名称是否正确。'
    },
    help_info: {
      success: `{{ message }}\n\n🔧 功能介绍：\n{{ features }}\n\n💡 使用示例：\n{{ examples }}\n\n📞 {{ contact }}`,
      error: '帮助信息加载失败，请稍后重试。'
    },
    unknown: {
      error: '抱歉，我无法理解您的请求。请输入"帮助"获取使用说明。'
    }
  };

  const template = responseTemplates[queryType] || responseTemplates.unknown;

  if (result.error) {
    return {
      success: false,
      message: template.error,
      timestamp: new Date().toISOString()
    };
  }

  // 模板变量替换
  let message = template.success;
  for (const [key, value] of Object.entries(result)) {
    const placeholder = `{{ ${key} }}`;
    if (Array.isArray(value)) {
      message = message.replace(placeholder, value.map(v => `• ${v}`).join('\n'));
    } else {
      message = message.replace(placeholder, value);
    }
  }

  return {
    success: true,
    message: message,
    data: result,
    timestamp: new Date().toISOString()
  };
};
```

## n8n工作流使用说明

### 1. 导入工作流

1. 打开n8n管理界面
2. 点击"Import from file"或"Import from URL"
3. 选择生成的`data_agent_workflow.json`文件
4. 点击"Import"完成导入

### 2. 配置工作流

```javascript
/**
 * 工作流配置说明
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增配置说明
 * @description n8n工作流的配置和使用说明
 */

// Webhook配置
const webhookConfig = {
  path: "agent-query",  // 访问路径
  method: "POST",       // 请求方法
  responseMode: "responseNode"  // 响应模式
};

// 请求示例
const requestExample = {
  url: "http://your-n8n-domain/webhook/agent-query",
  method: "POST",
  headers: {
    "Content-Type": "application/json"
  },
  body: {
    query: "查询基金 000001",
    userId: "user123"
  }
};

// 响应示例
const responseExample = {
  success: true,
  message: "📊 基金信息查询结果：\n\n🏷️ 基金代码：000001\n📈 基金名称：华夏成长混合\n👤 基金经理：张三\n💰 基金规模：15.6亿\n📋 基金类型：混合型基金\n💵 净值：1.2345\n📊 日涨跌：+0.56%",
  data: {
    fundCode: "000001",
    fundName: "华夏成长混合",
    manager: "张三",
    scale: "15.6亿",
    type: "混合型基金",
    netValue: "1.2345",
    dayChange: "+0.56%"
  },
  timestamp: "2025-08-01T14:51:59.000Z",
  responseType: "fund_data"
};
```

### 3. 支持的查询类型

```javascript
/**
 * 支持的查询类型和示例
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @description 详细的查询类型说明
 */

const supportedQueries = {
  // 基金查询
  fund_query: {
    examples: [
      "查询基金 000001",
      "华夏成长基金信息",
      "基金代码 000002",
      "000001基金怎么样"
    ],
    parameters: ["fundCode", "fundName"],
    response: "基金详细信息"
  },

  // 基金经理查询
  manager_query: {
    examples: [
      "基金经理张三",
      "查询经理信息",
      "管理人详情"
    ],
    parameters: ["managerName"],
    response: "基金经理信息"
  },

  // 帮助信息
  help: {
    examples: [
      "帮助",
      "help",
      "使用说明",
      "怎么使用"
    ],
    parameters: [],
    response: "使用帮助和功能说明"
  }
};
```

## 测试和验证

### 1. 工作流测试

```javascript
/**
 * 工作流测试用例
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增测试用例
 * @description Agent工作流的测试用例和验证方法
 */

const testCases = [
  {
    name: "基金代码查询测试",
    input: {
      query: "查询基金 000001",
      userId: "test_user_1"
    },
    expectedIntent: "fund_query",
    expectedParams: {
      fundCode: "000001"
    },
    expectedResponse: {
      success: true,
      responseType: "fund_data"
    }
  },
  {
    name: "基金名称查询测试",
    input: {
      query: "华夏成长基金信息",
      userId: "test_user_2"
    },
    expectedIntent: "fund_query",
    expectedParams: {
      fundName: "华夏成长基金"
    },
    expectedResponse: {
      success: true,
      responseType: "fund_data"
    }
  },
  {
    name: "帮助信息测试",
    input: {
      query: "帮助",
      userId: "test_user_3"
    },
    expectedIntent: "help",
    expectedParams: {},
    expectedResponse: {
      success: true,
      responseType: "help_info"
    }
  },
  {
    name: "未知查询测试",
    input: {
      query: "今天天气怎么样",
      userId: "test_user_4"
    },
    expectedIntent: "unknown",
    expectedParams: {},
    expectedResponse: {
      success: false
    }
  }
];
```

### 2. 性能优化建议

```javascript
/**
 * 性能优化配置
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增性能优化建议
 * @description Agent工作流的性能优化建议
 */

const performanceOptimization = {
  // 缓存配置
  cache: {
    enabled: true,
    ttl: 300, // 5分钟缓存
    maxSize: 1000, // 最大缓存条目数
    strategy: "LRU" // 最近最少使用策略
  },

  // 并发控制
  concurrency: {
    maxConcurrentRequests: 10,
    requestTimeout: 30000, // 30秒超时
    retryAttempts: 3,
    retryDelay: 1000 // 1秒重试延迟
  },

  // 资源限制
  resources: {
    maxMemoryUsage: "512MB",
    maxExecutionTime: 60000, // 60秒最大执行时间
    maxResponseSize: "10MB"
  }
};
```

## 扩展功能

### 1. 自定义意图扩展

```javascript
/**
 * 自定义意图扩展配置
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增扩展功能
 * @description 如何扩展新的意图识别和处理逻辑
 */

const customIntentExtension = {
  // 新增意图类型
  newIntents: {
    'stock_query': {
      patterns: ['股票', '查询股票', '股价', '股票代码'],
      handler: 'stock-query-handler',
      parameters: ['stockCode', 'stockName']
    },
    'market_analysis': {
      patterns: ['市场分析', '行情分析', '趋势'],
      handler: 'market-analysis-handler',
      parameters: ['timeRange', 'marketType']
    }
  },

  // 扩展参数提取规则
  parameterRules: {
    stockCode: /[0-9]{6}/g,
    stockName: /([A-Za-z\u4e00-\u9fa5]+股票)/g,
    timeRange: /(今日|本周|本月|今年)/g
  },

  // 新增处理节点模板
  handlerTemplate: {
    type: "n8n-nodes-base.code",
    typeVersion: 2,
    parameters: {
      mode: "runOnceForAllItems",
      jsCode: `
        // 自定义处理逻辑
        const data = $input.all()[0].json;
        const params = data.parameters;

        // 在这里添加具体的业务逻辑
        const result = processCustomQuery(params);

        return [{
          json: {
            ...data,
            queryResult: result,
            queryType: 'custom_query',
            success: true
          }
        }];
      `
    }
  }
};
```

### 2. 集成外部API

```javascript
/**
 * 外部API集成配置
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增API集成功能
 * @description 如何集成外部数据源和API服务
 */

const externalApiIntegration = {
  // API配置
  apis: {
    fundApi: {
      baseUrl: "https://api.fund-data.com",
      headers: {
        "Authorization": "Bearer {{ $env.FUND_API_TOKEN }}",
        "Content-Type": "application/json"
      },
      endpoints: {
        fundInfo: "/v1/funds/{fundCode}",
        managerInfo: "/v1/managers/{managerId}",
        performance: "/v1/funds/{fundCode}/performance"
      }
    },
    stockApi: {
      baseUrl: "https://api.stock-data.com",
      headers: {
        "X-API-Key": "{{ $env.STOCK_API_KEY }}",
        "Content-Type": "application/json"
      },
      endpoints: {
        stockInfo: "/v1/stocks/{stockCode}",
        realtime: "/v1/stocks/{stockCode}/realtime",
        history: "/v1/stocks/{stockCode}/history"
      }
    }
  },

  // HTTP请求节点配置
  httpRequestNode: {
    type: "n8n-nodes-base.httpRequest",
    typeVersion: 4.2,
    parameters: {
      method: "GET",
      url: "{{ $json.apiUrl }}",
      sendHeaders: true,
      specifyHeaders: "json",
      jsonHeaders: "{{ $json.headers }}",
      options: {
        timeout: 30000,
        retry: {
          enabled: true,
          maxAttempts: 3,
          waitBetween: 1000
        }
      }
    }
  }
};
```

## 部署说明

### 1. 快速部署步骤

```bash
# 部署步骤说明
# <AUTHOR>
# @created 2025-08-01 14:51:59
# @modified 2025-08-01 - 简化部署步骤
# @description Data Agent工作流的快速部署指南

# 1. 启动n8n服务
npx n8n start

# 2. 访问n8n管理界面
# 浏览器打开: http://localhost:5678

# 3. 导入工作流
# 在n8n界面中选择 "Import from file"
# 上传 data_agent_workflow.json 文件

# 4. 激活工作流
# 点击工作流右上角的 "Active" 开关

# 5. 测试工作流
curl -X POST http://localhost:5678/webhook/agent-query \
  -H "Content-Type: application/json" \
  -d '{"query": "查询基金 000001", "userId": "test_user"}'
```

### 2. 环境变量配置

```javascript
/**
 * 环境变量配置说明
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增环境变量说明
 * @description 必要的环境变量配置
 */

const environmentConfig = {
  // n8n基础配置
  N8N_BASIC_AUTH_ACTIVE: "true",
  N8N_BASIC_AUTH_USER: "admin",
  N8N_BASIC_AUTH_PASSWORD: "your_password_here",

  // Webhook配置
  WEBHOOK_URL: "http://localhost:5678",
  N8N_HOST: "localhost",
  N8N_PORT: "5678",
  N8N_PROTOCOL: "http",

  // 可选：数据库配置（如需持久化）
  DB_TYPE: "sqlite", // 或 "postgres", "mysql"
  DB_SQLITE_DATABASE: "database.sqlite",

  // 可选：外部API配置
  FUND_API_TOKEN: "your_fund_api_token",
  STOCK_API_KEY: "your_stock_api_key",

  // 日志配置
  N8N_LOG_LEVEL: "info",
  N8N_LOG_OUTPUT: "console"
};
```

### 3. 生产环境配置

```yaml
# docker-compose.prod.yml
# <AUTHOR>
# @created 2025-08-01 14:51:59
# @modified 2025-08-01 - 生产环境配置
# @description 生产环境的Docker Compose配置

version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
      - N8N_HOST=${N8N_HOST}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://${N8N_HOST}
      - GENERIC_TIMEZONE=Asia/Shanghai
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./data_agent_workflow.json:/home/<USER>/.n8n/workflows/data_agent_workflow.json:ro
    networks:
      - n8n_network

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - n8n
    networks:
      - n8n_network

volumes:
  n8n_data:

networks:
  n8n_network:
    driver: bridge
```

## 使用示例

### 1. 基础查询示例

```bash
# 基金代码查询
curl -X POST http://localhost:5678/webhook/agent-query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "查询基金 000001",
    "userId": "user123"
  }'

# 基金名称查询
curl -X POST http://localhost:5678/webhook/agent-query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "华夏成长基金信息",
    "userId": "user123"
  }'

# 帮助信息查询
curl -X POST http://localhost:5678/webhook/agent-query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "帮助",
    "userId": "user123"
  }'
```

### 2. JavaScript集成示例

```javascript
/**
 * JavaScript客户端集成示例
 * <AUTHOR>
 * @created 2025-08-01 14:51:59
 * @modified 2025-08-01 - 新增客户端集成示例
 * @description 如何在前端应用中集成Data Agent
 */

class DataAgentClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
  }

  async query(userQuery, userId = 'anonymous') {
    try {
      const response = await fetch(`${this.baseUrl}/webhook/agent-query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: userQuery,
          userId: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      console.error('查询失败:', error);
      return {
        success: false,
        message: '查询服务暂时不可用，请稍后重试',
        error: error.message
      };
    }
  }
}

// 使用示例
const agent = new DataAgentClient('http://localhost:5678');

// 查询基金信息
agent.query('查询基金 000001', 'user123')
  .then(result => {
    console.log('查询结果:', result);
    if (result.success) {
      console.log('回复:', result.message);
    } else {
      console.error('查询失败:', result.message);
    }
  });
```

## 总结

Data Agent智能代理工作流系统通过简化的设计和模块化实现，提供了一个易于使用、可扩展的智能查询平台。系统具备以下特点：

1. **简单易用**: 通过自然语言交互，降低使用门槛
2. **智能识别**: 自动识别用户意图并提取关键参数
3. **快速响应**: 优化的工作流设计确保快速响应
4. **易于扩展**: 模块化设计支持快速添加新功能
5. **标准化**: 遵循n8n最佳实践和代码规范

### 快速开始

1. 下载 `data_agent_workflow.json` 文件
2. 在n8n中导入工作流
3. 激活工作流
4. 发送POST请求到webhook端点进行测试

该系统为智能客服、数据查询和自动化处理提供了强有力的技术支撑，可以广泛应用于金融、电商、客服等多个领域。