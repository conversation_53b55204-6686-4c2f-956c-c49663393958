/**
 * 智能机构持股查询 - Code节点网络请求实现
 * <AUTHOR>
 * @date 2025-01-15T08:30:00.000Z
 * @description 使用Code节点进行HTTP请求，自动尝试多个日期获取机构持股数据
 * @modified hayden 2025-01-15T08:30:00.000Z 优化日期策略：本司优先当前季度，对标支持向后兼容
 * @modified hayden 2025-07-10T11:15:05.351Z 优化请求字段：只请求必要的p00155_f004和p00155_f006字段
 * @modified hayden 2025-07-10T11:30:25.123Z 修复reportDate：使用dateInfo中的日期信息构建标准格式的报告期
 * @modified hayden 2025-07-16T14:30:51.581Z 优化reportDate显示：每个投资人显示实际找到数据的日期作为reportDate
 * @modified hayden 2025-07-16T14:45:32.891Z 修复reportDate日期问题：确保使用真实查询到数据的日期
 * 功能说明：
 * - 调用同花顺API获取机构持股数据
 * - 本司数据：优先使用当前季度，找到即停止
 * - 对标数据：支持向后兼容，当前季度找不到则尝试更早日期
 * - 支持查询本公司和对标公司的机构持股数据
 * - 为投资人添加标签：持有本司和持有对标
 * - 只返回investorTags和dateInfo数据
 */

try {
  // 获取输入数据
  const items = $input.all();
  
  // 获取前置节点的数据
  const accessToken = $('缓存acc token').item.json.access_token;
  const companyCode = $('查询公司筛选配置').item.json.companyCode;
  const benchmarkCompanyCodes = $('查询公司筛选配置').item.json.benchmarkCompanyCodes || '';
  const dateSequence = $('智能日期计算').item.json.dateSequence || [];
  const organizationId = $('成功数据格式化').item.json.organizationId || '';

  // 验证必要参数
  if (!accessToken) {
    throw new Error('缺少access_token，请检查token获取流程');
  }
  
  if (!companyCode) {
    throw new Error('缺少companyCode，请检查公司筛选配置');
  }
  
  if (!dateSequence || dateSequence.length === 0) {
    throw new Error('缺少dateSequence，请检查日期计算结果');
  }
  
  /**
   * 将日期格式化为标准报告期格式 YYYY-MM-DD
   * @param {string} dateStr - 格式为YYYYMMDD的日期字符串
   * @return {string} 格式为YYYY-MM-DD的日期字符串
   */
  function formatReportDate(dateStr) {
    if (!dateStr || dateStr.length !== 8) {
      return '';
    }
    return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;
  }
  
  // 记录所有尝试过的日期
  const attemptedDates = [];
  let finalResponse = null;
  let successDate = null;
  let successDateInfo = null;
  let lastAttemptedDateInfo = null;
  
  /**
   * 本司数据查询 - 优先使用当前季度
   * @description 按日期顺序尝试，找到有效数据即停止
   */
  for (const dateInfo of dateSequence) {
    const currentDate = dateInfo.date;
    attemptedDates.push(currentDate);
    lastAttemptedDateInfo = dateInfo;
    
    // 配置HTTP请求选项 - 本公司
    const options = {
      url: 'https://quantapi.51ifind.com/api/v1/data_pool',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'access_token': accessToken
      },
      body: {
        reportname: 'p00155',
        functionpara: {
          thscode: companyCode,
          date: currentDate
        },
        outputpara: 'p00155_f004,p00155_f006'
      },
      json: true,
      timeout: 30000,
      skipSslCertificateValidation: false
    };
    
    // 执行HTTP请求 - 本公司
    const response = await this.helpers.httpRequest(options);
    
    // 检查是否有有效的基金数据
    const hasValidData = response.errorcode !== -4001 && 
                        response.tables && 
                        response.tables.length > 0 && 
                        response.tables[0].table && 
                        response.tables[0].table.p00155_f004 && 
                        response.tables[0].table.p00155_f004.length > 0 &&
                        response.tables[0].table.p00155_f006 &&
                        response.tables[0].table.p00155_f006.some(type => type === "基金");

    if (hasValidData) {
      // 找到有数据的日期，保存结果并跳出循环
      finalResponse = response;
      successDate = currentDate;
      successDateInfo = dateInfo;
      break;
    }

    // 如果是最后一个日期且仍然没有数据，保存最后一次响应
    if (dateInfo === dateSequence[dateSequence.length - 1]) {
      finalResponse = response;
    }
  }
  
  // 如果所有日期都没有数据，使用最后一次响应
  if (!finalResponse) {
    throw new Error('所有日期都没有返回有效数据');
  }
  
  // 创建投资人标签数据
  const investorTags = [];
  const currentTimestamp = new Date().toISOString();
  
  // 处理本公司数据
  if (successDate && finalResponse.tables && finalResponse.tables.length > 0 && finalResponse.tables[0].table) {
    const tableData = finalResponse.tables[0].table;
    const recordCount = tableData.p00155_f004.length;
    // 格式化报告期日期 - 使用实际找到数据的日期
    const reportDate = formatReportDate(successDate);
    
    console.log(`本司(${companyCode})数据使用日期: ${successDate}, 格式化后: ${reportDate}`);

    // 转换表格数据为对象数组，只处理机构类型为"基金"的记录
    for (let i = 0; i < recordCount; i++) {
      if (tableData.p00155_f006[i] === "基金") {
        investorTags.push({
          investorCode: tableData.p00155_f004[i],
          fundInfo: tableData.p00155_f004[i],
          tagName: `持有本司(${companyCode})`,
          tagCategory: "system",
          modifiedAt: currentTimestamp,
          reportDate: reportDate // 使用本司实际找到数据的日期
        });
      }
    }
  }
  
  /**
   * 对标公司数据查询 - 支持向后兼容
   * @description 每个对标公司独立尝试所有日期，直到找到有效数据
   */
  if (benchmarkCompanyCodes) {
    // 解析对标公司代码
    const benchmarkCodes = typeof benchmarkCompanyCodes === 'string' 
      ? benchmarkCompanyCodes.split(',').map(code => code.trim()).filter(code => code)
      : Array.isArray(benchmarkCompanyCodes) ? benchmarkCompanyCodes : [];
    
    // 如果有有效的对标公司代码
    if (benchmarkCodes.length > 0) {
      // 对每个对标公司代码进行查询
      for (const benchmarkCode of benchmarkCodes) {
        if (!benchmarkCode) {
          continue;
        }
        
        let benchmarkSuccessDate = null;
        let benchmarkResponse = null;
        let benchmarkDateInfo = null;
        
        // 为对标公司尝试所有日期，支持向后兼容
        for (const dateInfo of dateSequence) {
          const currentDate = dateInfo.date;
          
          // 配置HTTP请求选项 - 对标公司
          const benchmarkOptions = {
            url: 'https://quantapi.51ifind.com/api/v1/data_pool',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'access_token': accessToken
            },
            body: {
              reportname: 'p00155',
              functionpara: {
                thscode: benchmarkCode,
                date: currentDate
              },
              outputpara: 'p00155_f004,p00155_f006'
            },
            json: true,
            timeout: 30000,
            skipSslCertificateValidation: false
          };
          
          try {
            // 执行HTTP请求 - 对标公司
            const response = await this.helpers.httpRequest(benchmarkOptions);
            
            // 检查是否有有效的基金数据
            const hasValidBenchmarkData = response.errorcode !== -4001 && 
                                        response.tables && 
                                        response.tables.length > 0 && 
                                        response.tables[0].table && 
                                        response.tables[0].table.p00155_f004 && 
                                        response.tables[0].table.p00155_f004.length > 0 &&
                                        response.tables[0].table.p00155_f006 &&
                                        response.tables[0].table.p00155_f006.some(type => type === "基金");
            
            if (hasValidBenchmarkData) {
              // 找到有数据的日期，保存结果并跳出循环
              benchmarkResponse = response;
              benchmarkSuccessDate = currentDate;
              benchmarkDateInfo = dateInfo;
              break;
            } 
            
            // 如果是最后一个日期且仍然没有数据，保存最后一次响应
            if (dateInfo === dateSequence[dateSequence.length - 1]) {
              benchmarkResponse = response;
            }
          } catch (benchmarkError) {
            console.error(`查询对标公司 ${benchmarkCode} 日期 ${currentDate} 数据失败: ${benchmarkError.message}`);
            // 继续尝试下一个日期
          }
        }
        
        // 处理对标公司数据（如果找到了有效数据）
        if (benchmarkSuccessDate && benchmarkResponse && benchmarkResponse.tables &&
            benchmarkResponse.tables.length > 0 && benchmarkResponse.tables[0].table) {

          const tableData = benchmarkResponse.tables[0].table;
          const recordCount = tableData.p00155_f004.length;
          // 格式化报告期日期 - 使用该对标公司实际找到数据的日期
          const reportDate = formatReportDate(benchmarkSuccessDate);
          
          console.log(`对标公司(${benchmarkCode})数据使用日期: ${benchmarkSuccessDate}, 格式化后: ${reportDate}`);

          // 转换表格数据为对象数组，只处理机构类型为"基金"的记录
          for (let i = 0; i < recordCount; i++) {
            if (tableData.p00155_f006[i] === "基金") {
              investorTags.push({
                investorCode: tableData.p00155_f004[i],
                fundInfo: tableData.p00155_f004[i],
                tagName: `持有对标(${benchmarkCode})`,
                tagCategory: "system",
                modifiedAt: currentTimestamp,
                reportDate: reportDate // 使用该对标公司实际找到数据的日期
              });
            }
          }
        }
      }
    }
  } 
  
  // 准备返回结果对象
  const result = {
    organizationId: organizationId,
    dateInfo: successDateInfo || lastAttemptedDateInfo,
    investorTags: investorTags,
    attemptedDates: attemptedDates,
    hasValidData: !!successDate
  };
  
  return [{
    json: result
  }];
  
} catch (error) {
  // 详细的错误处理
  console.error(`智能机构持股查询失败: ${error.message}`);
  
  // 构建简化的错误响应
  const errorResponse = {
    error: true,
    errorMessage: error.message,
    organizationId: $('成功数据格式化').item?.json?.organizationId || '',
    dateInfo: null,
    investorTags: []
  };
  
  return [{
    json: errorResponse
  }];
}
