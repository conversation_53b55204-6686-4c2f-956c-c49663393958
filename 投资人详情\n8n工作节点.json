{
  "nodes": [
    {
      "parameters": {
        "method": "POST",
        "url": "https://quantapi.51ifind.com/api/v1/basic_data_service",
        "sendHeaders": true,
        "specifyHeaders": "json",
        "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"}",
        "sendBody": true,
        "contentType": "raw",
        "rawContentType": "formData",
        "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_rzts_by_tenure_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_name_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_managerid_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_gender_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_education_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_age_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_fund_manager_max_ages_fund\"},{\"indicator\":\"ths_rzjjzgm_fund\",\"indiparams\":[\"100\",\"1\",\"101\",\"1\"]},{\"indicator\":\"ths_zrjjs_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_fund_manager_invest_style_fund\",\"indiparams\":[\"100\",\"1\",\"20250713\",\"101\"]},{\"indicator\":\"ths_theme_style_fund\",\"indiparams\":[\"100\",\"1\",\"20250713\",\"101\"]},{\"indicator\":\"ths_service_funds_num_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_managerid_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_jlhjcs_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_resume_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]}]}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        320,
        2820
      ],
      "id": "dd917ad5-d908-496d-be33-f4278cafbc37",
      "name": "基金经理信息"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://quantapi.51ifind.com/api/v1/data_pool",
        "sendHeaders": true,
        "specifyHeaders": "json",
        "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}",
        "sendBody": true,
        "contentType": "raw",
        "rawContentType": "formData",
        "body": "={\"reportname\":\"p00512\",\"functionpara\":{\"thscode\":\"{{ $json.tables[0].thscode }}\",\"p0\":\"{{ $json.tables[0].table.ths_managerid_fund }}\"},\"outputpara\":\"jydm,jydm_mc,p00512_f009\"}",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        760,
        2860
      ],
      "id": "3d8414bc-1a56-4f97-8907-45857036dc8c",
      "name": "基金经理管理基金（TOP10）"
    },
    {
      "parameters": {
        "jsCode": "// 优化后的筛选逻辑 - 提前排序和过滤
const inputItems = items;

// 按基金规模排序并取TOP10
const sortedFunds = inputItems
  .filter(item => {
    const scale = parseFloat(item.json.管理基金规模);
    return !isNaN(scale) && scale > 0; // 提前过滤无效数据
  })
  .sort((a, b) => {
    const scaleA = parseFloat(a.json.管理基金规模) || 0;
    const scaleB = parseFloat(b.json.管理基金规模) || 0;
    return scaleB - scaleA; // 降序排列
  })
  .slice(0, 10) // 只保留TOP10
  .map((item, index) => ({
    json: {
      排名: (index + 1).toString(),
      基金名称: item.json.管理基金名称,
      基金规模: parseFloat(item.json.管理基金规模).toFixed(2)
    }
  }));

return sortedFunds;"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1600,
        2860
      ],
      "id": "19d50f93-c2b3-4b7c-b9fa-aa67e4041bdc",
      "name": "筛选管理基金（TOP10）"
    },
    {
      "parameters": {
        "jsCode": "// 优化后的数据扁平化 - 只处理必要字段
const inputItems = items;
const results = [];

for (const item of inputItems) {
  const tables = item.json?.tables;
  
  if (!tables || !Array.isArray(tables)) {
    continue; // 跳过无效数据
  }

  // 只提取TOP10相关数据，提前过滤
  tables.slice(0, 10).forEach(table => {
    if (table?.table?.jydm && table.table.jydm_mc) {
      results.push({
        json: {
          jydm: table.table.jydm,
          jydm_mc: table.table.jydm_mc,
          // 只保留必要字段，减少数据传输
          originalData: {
            access_token: item.json.access_token
          }
        }
      });
    }
  });
}

return results.slice(0, 10); // 确保只返回TOP10
"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1040,
        2860
      ],
      "id": "301fc871-c8e3-419b-96ae-e7e1824e17d7",
      "name": "数据扁平化"
    },
    {
      "parameters": {
        "jsCode": "// 优化后的基金规模批量处理
const inputItems = items;
const accessToken = $('If').first().json.access_token;

// 提取所有jydm，批量处理
const jydmList = inputItems
  .map(item => item.json.jydm)
  .filter(jydm => jydm && jydm.trim() !== '');

if (jydmList.length === 0) {
  return inputItems.map(item => ({
    json: { ...item.json, error: "无有效jydm数据" }
  }));
}

// 并行发送所有请求
const promises = jydmList.map(async (jydm, index) => {
  try {
    const response = await this.helpers.httpRequest({
      url: 'https://quantapi.51ifind.com/api/v1/data_pool',
      method: 'POST',
      headers: {
        "Content-Type": "application/json",
        "access_token": accessToken,
        "ifindlang": "cn"
      },
      body: {
        "reportname": "p00407",
        "functionpara": { "jjlb": jydm },
        "outputpara": "jydm,jydm_mc,p00407_f009"
      },
      json: true
    });

    return {
      index,
      jydm,
      data: response?.tables?.[0]?.table || null
    };
  } catch (error) {
    return { index, jydm, error: error.message };
  }
});

// 等待所有请求完成
const results = await Promise.all(promises);

// 合并结果
return inputItems.map((item, index) => {
  const result = results.find(r => r.jydm === item.json.jydm);
  
  if (result?.data) {
    return {
      json: {
        ...item.json,
        管理基金代码: result.data.jydm,
        管理基金名称: result.data.jydm_mc,
        管理基金规模: result.data.p00407_f009
      }
    };
  }
  
  return {
    json: {
      ...item.json,
      error: result?.error || "未获取到数据"
    }
  };
});"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1320,
        2860
      ],
      "id": "9ccb9fed-e005-4d93-bade-9c809cf5b543",
      "name": "基金规模批量处理"
    },
    {
      "parameters": {
        "content": "数据消耗量51",
        "width": 220
      },
      "type": "n8n-nodes-base.stickyNote",
      "position": [
        700,
        2800
      ],
      "typeVersion": 1,
      "id": "46d021b6-d34f-4c66-9f3d-9955bb88e855",
      "name": "Sticky Note"
    }
  ],
  "connections": {
    "基金经理信息": {
      "main": [
        [
          {
            "node": "数据扁平化",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "数据扁平化": {
      "main": [
        [
          {
            "node": "基金规模批量处理",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "基金规模批量处理": {
      "main": [
        [
          {
            "node": "筛选管理基金（TOP10）",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "meta": {
    "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"
  }
}
