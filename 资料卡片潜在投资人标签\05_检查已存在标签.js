/**
 * 检查已存在的潜在投资人标签
 * <AUTHOR>
 * @date 2025-07-22T10:00:00.000Z
 * @description 查询数据库中已存在的潜在投资人标签，避免重复插入
 */

try {
  // 获取上游数据
  const matchedFunds = $('行业匹配判断').first().json.matchedFunds || [];
  const organizationId = $('成功数据格式化').first().json.organizationId || '';
  
  if (matchedFunds.length === 0) {
    return {
      errorcode: 0,
      errmsg: "",
      message: "没有需要检查的基金",
      existingTags: [],
      newFunds: [],
      newCount: 0
    };
  }

  // 构建投资人代码列表用于查询
  const investorCodes = matchedFunds.map(fund => `'${fund.investorCode}'`).join(',');
  
  // 构建SQL查询语句
  const sql = `
    SELECT "investorCode", "tagName", "organizationId"
    FROM investor_tag 
    WHERE "organizationId" = '${organizationId}' 
    AND "investorCode" IN (${investorCodes})
    AND "tagName" LIKE '潜在投资人%'
  `;

  return {
    errorcode: 0,
    errmsg: "",
    message: "准备检查已存在标签",
    sql: sql,
    matchedFunds: matchedFunds,
    organizationId: organizationId
  };

} catch (error) {
  return {
    errorcode: -1,
    errmsg: `检查已存在标签异常: ${error.message}`,
    sql: "",
    matchedFunds: [],
    newCount: 0
  };
}
