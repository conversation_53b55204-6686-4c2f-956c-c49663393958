/**
 * 准备SQL插入语句
 * <AUTHOR>
 * @date 2025-01-15T10:00:00.000Z
 * @description 为新的潜在投资人标签准备SQL插入语句
 */

try {
  // 获取新标签数据
  const newTags = $('生成潜在投资人标签').item.json.newTags || [];
  
  if (newTags.length === 0) {
    return {
      success: false,
      needDatabaseUpdate: false,
      message: '没有需要插入的新标签',
      sql: '',
      newTagsCount: 0
    };
  }

  // 准备SQL插入语句
  const valueStrings = newTags.map(tag => {
    // 将tagMetadata转换为JSON字符串并转义单引号
    const tagMetadataJson = JSON.stringify(tag.tagMetadata).replace(/'/g, "''");
    
    // 处理companyFilterId可能为null的情况
    const filterIdValue = tag.companyFilterId ? `'${tag.companyFilterId}'` : 'NULL';

    return `('${tag.id}', '${tag.investorCode}', '${tag.tagName}', '${tag.tagCategory}', '${tag.organizationId}', ${filterIdValue}, '${tagMetadataJson}', '${tag.modifiedAt}')`;
  });

  // 构建完整的SQL语句
  const sql = `INSERT INTO investor_tag ("id", "investorCode", "tagName", "tagCategory", "organizationId", "companyFilterId", "tagMetadata", "modifiedAt") VALUES ${valueStrings.join(', ')} ON CONFLICT ("organizationId", "investorCode", "tagName") DO UPDATE SET "modifiedAt" = EXCLUDED."modifiedAt", "tagMetadata" = EXCLUDED."tagMetadata";`;

  return {
    success: true,
    needDatabaseUpdate: true,
    message: `准备插入${newTags.length}条潜在投资人标签`,
    sql: sql,
    newTagsCount: newTags.length,
    newTags: newTags.map(tag => ({
      investorCode: tag.investorCode,
      tagName: tag.tagName,
      stockIndustry: tag.tagMetadata.stockIndustry
    }))
  };

} catch (error) {
  return {
    success: false,
    needDatabaseUpdate: false,
    error: {
      code: 'SQL_PREPARATION_ERROR',
      message: '准备SQL语句时发生错误',
      details: error.message || '未知错误'
    }
  };
}
